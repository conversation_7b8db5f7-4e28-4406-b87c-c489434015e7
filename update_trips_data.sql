-- Script pour mettre à jour les données de voyages avec des dates actuelles
USE train;

-- Supprimer les anciens voyages
DELETE FROM trips;

-- Insérer des voyages avec des dates actuelles et futures
INSERT INTO trips (departure_city, destination_city, departure_time, arrival_time, price, available_seats) VALUES
-- Voyages d'aujourd'hui
('Paris', 'Lyon', DATE_ADD(CURDATE(), INTERVAL 8 HOUR), DATE_ADD(CURDATE(), INTERVAL 10 HOUR + 30 MINUTE), 45.50, 120),
('Lyon', 'Marseille', DATE_ADD(CURDATE(), INTERVAL 14 HOUR), DATE_ADD(CURDATE(), INTERVAL 16 HOUR + 45 MINUTE), 35.00, 80),
('Paris', 'Marseille', DATE_ADD(CURDATE(), INTERVAL 9 HOUR + 15 MINUTE), DATE_ADD(CURDATE(), INTERVAL 12 HOUR + 30 MINUTE), 75.00, 150),
('Marseille', 'Nice', DATE_ADD(CURDATE(), INTERVAL 16 HOUR), DATE_ADD(CURDATE(), INTERVAL 18 HOUR + 30 MINUTE), 25.00, 60),
('Nice', 'Monaco', DATE_ADD(CURDATE(), INTERVAL 19 HOUR), DATE_ADD(CURDATE(), INTERVAL 19 HOUR + 45 MINUTE), 15.00, 40),

-- Voyages de demain
('Paris', 'Lyon', DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 7 HOUR, DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 9 HOUR + 30 MINUTE, 45.50, 100),
('Lyon', 'Paris', DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 15 HOUR, DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 17 HOUR + 30 MINUTE, 45.50, 90),
('Paris', 'Bordeaux', DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 10 HOUR, DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 13 HOUR + 15 MINUTE, 65.00, 110),
('Bordeaux', 'Toulouse', DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 16 HOUR, DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 18 HOUR + 30 MINUTE, 30.00, 70),
('Paris', 'Strasbourg', DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 11 HOUR, DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 14 HOUR + 45 MINUTE, 55.00, 85),

-- Voyages d'après-demain
('Lyon', 'Marseille', DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 8 HOUR, DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 10 HOUR + 45 MINUTE, 35.00, 95),
('Marseille', 'Lyon', DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 17 HOUR, DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 19 HOUR + 45 MINUTE, 35.00, 75),
('Paris', 'Lille', DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 9 HOUR, DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 10 HOUR + 15 MINUTE, 40.00, 130),
('Lille', 'Paris', DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 18 HOUR, DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 19 HOUR + 15 MINUTE, 40.00, 120),
('Toulouse', 'Montpellier', DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 12 HOUR, DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 14 HOUR + 30 MINUTE, 28.00, 65),

-- Voyages dans 3 jours
('Paris', 'Nantes', DATE_ADD(CURDATE(), INTERVAL 3 DAY) + INTERVAL 8 HOUR + 30 MINUTE, DATE_ADD(CURDATE(), INTERVAL 3 DAY) + INTERVAL 10 HOUR + 45 MINUTE, 50.00, 105),
('Nantes', 'Paris', DATE_ADD(CURDATE(), INTERVAL 3 DAY) + INTERVAL 16 HOUR, DATE_ADD(CURDATE(), INTERVAL 3 DAY) + INTERVAL 18 HOUR + 15 MINUTE, 50.00, 95),
('Lyon', 'Dijon', DATE_ADD(CURDATE(), INTERVAL 3 DAY) + INTERVAL 13 HOUR, DATE_ADD(CURDATE(), INTERVAL 3 DAY) + INTERVAL 15 HOUR), 32.00, 80),
('Dijon', 'Lyon', DATE_ADD(CURDATE(), INTERVAL 3 DAY) + INTERVAL 19 HOUR, DATE_ADD(CURDATE(), INTERVAL 3 DAY) + INTERVAL 21 HOUR), 32.00, 75),

-- Voyages dans 4 jours
('Paris', 'Rennes', DATE_ADD(CURDATE(), INTERVAL 4 DAY) + INTERVAL 9 HOUR, DATE_ADD(CURDATE(), INTERVAL 4 DAY) + INTERVAL 11 HOUR + 30 MINUTE, 48.00, 90),
('Rennes', 'Paris', DATE_ADD(CURDATE(), INTERVAL 4 DAY) + INTERVAL 17 HOUR, DATE_ADD(CURDATE(), INTERVAL 4 DAY) + INTERVAL 19 HOUR + 30 MINUTE, 48.00, 85),

-- Voyages dans 5 jours
('Paris', 'Tours', DATE_ADD(CURDATE(), INTERVAL 5 DAY) + INTERVAL 10 HOUR, DATE_ADD(CURDATE(), INTERVAL 5 DAY) + INTERVAL 11 HOUR + 15 MINUTE, 35.00, 100),
('Tours', 'Paris', DATE_ADD(CURDATE(), INTERVAL 5 DAY) + INTERVAL 18 HOUR + 30 MINUTE, DATE_ADD(CURDATE(), INTERVAL 5 DAY) + INTERVAL 19 HOUR + 45 MINUTE, 35.00, 95),

-- Voyages dans 6 jours
('Marseille', 'Montpellier', DATE_ADD(CURDATE(), INTERVAL 6 DAY) + INTERVAL 11 HOUR, DATE_ADD(CURDATE(), INTERVAL 6 DAY) + INTERVAL 12 HOUR + 45 MINUTE, 22.00, 70),
('Montpellier', 'Marseille', DATE_ADD(CURDATE(), INTERVAL 6 DAY) + INTERVAL 16 HOUR + 30 MINUTE, DATE_ADD(CURDATE(), INTERVAL 6 DAY) + INTERVAL 18 HOUR + 15 MINUTE, 22.00, 65),

-- Voyages dans 7 jours
('Paris', 'Reims', DATE_ADD(CURDATE(), INTERVAL 7 DAY) + INTERVAL 8 HOUR, DATE_ADD(CURDATE(), INTERVAL 7 DAY) + INTERVAL 9 HOUR + 30 MINUTE, 38.00, 110),
('Reims', 'Paris', DATE_ADD(CURDATE(), INTERVAL 7 DAY) + INTERVAL 17 HOUR, DATE_ADD(CURDATE(), INTERVAL 7 DAY) + INTERVAL 18 HOUR + 30 MINUTE, 38.00, 105);

-- Afficher le nombre de voyages créés
SELECT COUNT(*) as total_trips FROM trips;

-- Afficher quelques exemples
SELECT id, departure_city, destination_city, 
       DATE_FORMAT(departure_time, '%Y-%m-%d %H:%i') as departure,
       DATE_FORMAT(arrival_time, '%Y-%m-%d %H:%i') as arrival,
       price, available_seats 
FROM trips 
ORDER BY departure_time 
LIMIT 10;
