-- Script pour créer la table des réservations
-- À exécuter dans votre base de données MySQL trainapp

USE trainapp;

-- Créer la table des réservations
CREATE TABLE IF NOT EXISTS reservations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    trip_id INT NOT NULL,
    number_of_seats INT NOT NULL DEFAULT 1,
    total_price DECIMAL(10, 2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'CONFIRMED',
    reservation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Contraintes de clés étrangères
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (trip_id) REFERENCES trips(id) ON DELETE CASCADE,
    
    -- Index pour améliorer les performances
    INDEX idx_user_id (user_id),
    INDEX idx_trip_id (trip_id),
    INDEX idx_status (status),
    INDEX idx_reservation_date (reservation_date)
);

-- Insérer quelques données de test (optionnel)
-- Assurez-vous d'avoir des utilisateurs et des trips existants avant d'exécuter ces insertions

-- Exemple de réservations de test (décommentez si vous voulez des données de test)
/*
INSERT INTO reservations (user_id, trip_id, number_of_seats, total_price, status) VALUES
(2, 1, 2, 120.00, 'CONFIRMED'),
(2, 2, 1, 85.00, 'CONFIRMED'),
(3, 1, 1, 60.00, 'CANCELLED'),
(3, 3, 3, 225.00, 'CONFIRMED');
*/

-- Vérifier la structure de la table
DESCRIBE reservations;

-- Afficher les contraintes
SELECT 
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM 
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
    TABLE_SCHEMA = 'trainapp' 
    AND TABLE_NAME = 'reservations'
    AND REFERENCED_TABLE_NAME IS NOT NULL;
