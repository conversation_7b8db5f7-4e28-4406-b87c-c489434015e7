-- Script complet pour créer toutes les tables de TrainApp
-- À exécuter dans votre base de données MySQL trainapp

USE trainapp;

-- =====================================================
-- 1. TABLE GARES
-- =====================================================
CREATE TABLE IF NOT EXISTS gares (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    ville VARCHAR(100) NOT NULL,
    adresse TEXT,
    code_postal VARCHAR(10),
    region VARCHAR(100),
    pays VARCHAR(100) DEFAULT 'France',
    latitude DECIMAL(10, 8) DEFAULT 0,
    longitude DECIMAL(11, 8) DEFAULT 0,
    telephone VARCHAR(20),
    email VARCHAR(100),
    active BOOLEAN DEFAULT TRUE,
    capacite_max INT DEFAULT 0,
    services TEXT, -- JSON des services disponibles
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Index pour améliorer les performances
    INDEX idx_ville (ville),
    INDEX idx_nom (nom),
    INDEX idx_active (active),
    INDEX idx_region (region)
);

-- =====================================================
-- 2. TABLE VOYAGES (remplace trips)
-- =====================================================
CREATE TABLE IF NOT EXISTS voyages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero_voyage VARCHAR(20) NOT NULL UNIQUE,
    gare_depart INT NOT NULL,
    gare_arrivee INT NOT NULL,
    heure_depart TIMESTAMP NOT NULL,
    heure_arrivee TIMESTAMP NOT NULL,
    type_train VARCHAR(50) DEFAULT 'TER', -- TGV, TER, Intercités, etc.
    places_total INT NOT NULL DEFAULT 0,
    places_disponibles INT NOT NULL DEFAULT 0,
    prix_base DECIMAL(10, 2) NOT NULL,
    statut VARCHAR(30) DEFAULT 'PROGRAMME', -- PROGRAMME, EN_COURS, TERMINE, ANNULE, RETARDE
    motif_annulation TEXT,
    retard_minutes INT DEFAULT 0,
    voie VARCHAR(10),
    reservation_ouverte BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Contraintes de clés étrangères
    FOREIGN KEY (gare_depart) REFERENCES gares(id) ON DELETE RESTRICT,
    FOREIGN KEY (gare_arrivee) REFERENCES gares(id) ON DELETE RESTRICT,
    
    -- Index pour améliorer les performances
    INDEX idx_numero_voyage (numero_voyage),
    INDEX idx_gare_depart (gare_depart),
    INDEX idx_gare_arrivee (gare_arrivee),
    INDEX idx_heure_depart (heure_depart),
    INDEX idx_statut (statut),
    INDEX idx_type_train (type_train),
    
    -- Contraintes de validation
    CHECK (heure_arrivee > heure_depart),
    CHECK (places_disponibles >= 0),
    CHECK (places_disponibles <= places_total),
    CHECK (prix_base >= 0)
);

-- =====================================================
-- 3. TABLE TRIPS (pour compatibilité avec l'existant)
-- =====================================================
-- Garder la table trips existante mais ajouter des références aux gares
ALTER TABLE trips 
ADD COLUMN gare_depart_id INT NULL,
ADD COLUMN gare_arrivee_id INT NULL,
ADD FOREIGN KEY (gare_depart_id) REFERENCES gares(id) ON DELETE SET NULL,
ADD FOREIGN KEY (gare_arrivee_id) REFERENCES gares(id) ON DELETE SET NULL;

-- =====================================================
-- 4. TABLE RESERVATIONS (mise à jour)
-- =====================================================
-- Mettre à jour la table reservations existante
ALTER TABLE reservations 
ADD COLUMN last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN cancellation_reason TEXT NULL,
ADD COLUMN ticket_number VARCHAR(20) NULL,
ADD COLUMN is_modifiable BOOLEAN DEFAULT TRUE;

-- Rendre ticket_number unique après avoir généré des valeurs
UPDATE reservations 
SET ticket_number = CONCAT('TKT-', UPPER(SUBSTRING(MD5(CONCAT(id, user_id, trip_id)), 1, 8)))
WHERE ticket_number IS NULL;

ALTER TABLE reservations 
MODIFY COLUMN ticket_number VARCHAR(20) NOT NULL,
ADD UNIQUE INDEX idx_ticket_number (ticket_number);

-- Modifier la colonne status pour accepter les nouveaux statuts
ALTER TABLE reservations 
MODIFY COLUMN status VARCHAR(30) NOT NULL DEFAULT 'CONFIRMED';

-- =====================================================
-- 5. TABLE PAIEMENTS
-- =====================================================
CREATE TABLE IF NOT EXISTS paiements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    reservation_id INT NOT NULL,
    user_id INT NOT NULL,
    montant DECIMAL(10, 2) NOT NULL,
    methode_paiement VARCHAR(30) NOT NULL, -- CARTE_CREDIT, PAYPAL, VIREMENT, ESPECES
    statut VARCHAR(30) DEFAULT 'EN_ATTENTE', -- EN_ATTENTE, VALIDE, REFUSE, REMBOURSE, ANNULE
    numero_transaction VARCHAR(100) UNIQUE,
    reference_externe VARCHAR(100),
    date_paiement TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_validation TIMESTAMP NULL,
    date_remboursement TIMESTAMP NULL,
    motif_refus TEXT,
    motif_remboursement TEXT,
    montant_rembourse DECIMAL(10, 2) DEFAULT 0,
    frais_transaction DECIMAL(10, 2) DEFAULT 0,
    devise_origine VARCHAR(3) DEFAULT 'EUR',
    taux_change DECIMAL(10, 6) DEFAULT 1.0,
    adresse_facturation TEXT,
    nom_titulaire VARCHAR(100),
    dernier_quatre_chiffres VARCHAR(4), -- Pour les cartes
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Contraintes de clés étrangères
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Index pour améliorer les performances
    INDEX idx_reservation_id (reservation_id),
    INDEX idx_user_id (user_id),
    INDEX idx_statut (statut),
    INDEX idx_methode_paiement (methode_paiement),
    INDEX idx_date_paiement (date_paiement),
    INDEX idx_numero_transaction (numero_transaction),
    
    -- Contraintes de validation
    CHECK (montant >= 0),
    CHECK (montant_rembourse >= 0),
    CHECK (frais_transaction >= 0),
    CHECK (taux_change > 0)
);

-- =====================================================
-- 6. TABLE DEMANDES_ANNULATION
-- =====================================================
CREATE TABLE IF NOT EXISTS demandes_annulation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    reservation_id INT NOT NULL,
    user_id INT NOT NULL,
    motif TEXT NOT NULL,
    statut VARCHAR(30) DEFAULT 'EN_ATTENTE', -- EN_ATTENTE, APPROUVEE, REJETEE
    date_demande TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_traitement TIMESTAMP NULL,
    admin_id INT NULL, -- ID de l'admin qui a traité la demande
    commentaire_admin TEXT,
    montant_remboursement DECIMAL(10, 2) DEFAULT 0,
    frais_annulation DECIMAL(10, 2) DEFAULT 0,
    
    -- Contraintes de clés étrangères
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Index
    INDEX idx_reservation_id (reservation_id),
    INDEX idx_user_id (user_id),
    INDEX idx_statut (statut),
    INDEX idx_date_demande (date_demande),
    
    -- Contrainte unique : une seule demande par réservation
    UNIQUE KEY unique_reservation_demande (reservation_id)
);

-- =====================================================
-- 7. DONNÉES DE TEST
-- =====================================================

-- Insérer des gares de test
INSERT INTO gares (nom, ville, adresse, code_postal, region, latitude, longitude, telephone, email, capacite_max, services) VALUES
('Gare de Lyon', 'Paris', 'Place Louis-Armand', '75012', 'Île-de-France', 48.8444, 2.3739, '01 23 45 67 89', '<EMAIL>', 5000, '{"wifi": true, "restaurant": true, "boutiques": true, "parking": true}'),
('Gare Part-Dieu', 'Lyon', 'Place Charles Béraudier', '69003', 'Auvergne-Rhône-Alpes', 45.7606, 4.8598, '04 12 34 56 78', '<EMAIL>', 3000, '{"wifi": true, "restaurant": true, "boutiques": true}'),
('Gare Saint-Charles', 'Marseille', 'Square Narvik', '13001', 'Provence-Alpes-Côte d\'Azur', 43.3030, 5.3811, '04 98 76 54 32', '<EMAIL>', 2500, '{"wifi": true, "restaurant": false, "boutiques": true}'),
('Gare Centrale', 'Lille', 'Place François Mitterrand', '59000', 'Hauts-de-France', 50.6365, 3.0635, '03 11 22 33 44', '<EMAIL>', 2000, '{"wifi": true, "restaurant": true}'),
('Gare Saint-Jean', 'Bordeaux', 'Rue Charles Domercq', '33800', 'Nouvelle-Aquitaine', 44.8258, -0.5570, '05 55 66 77 88', '<EMAIL>', 1800, '{"wifi": true, "boutiques": true}')
ON DUPLICATE KEY UPDATE id=id;

-- Mettre à jour les trips existants avec les références aux gares
UPDATE trips t 
JOIN gares g1 ON t.departure_city = g1.ville 
SET t.gare_depart_id = g1.id;

UPDATE trips t 
JOIN gares g2 ON t.destination_city = g2.ville 
SET t.gare_arrivee_id = g2.id;

-- Insérer des voyages de test
INSERT INTO voyages (numero_voyage, gare_depart, gare_arrivee, heure_depart, heure_arrivee, type_train, places_total, places_disponibles, prix_base) VALUES
('TGV8501', 1, 2, '2025-06-01 08:00:00', '2025-06-01 10:30:00', 'TGV', 300, 250, 45.50),
('TGV8502', 1, 3, '2025-06-01 09:00:00', '2025-06-01 12:15:00', 'TGV', 350, 280, 60.00),
('TER4501', 2, 3, '2025-06-01 14:30:00', '2025-06-01 18:45:00', 'TER', 150, 120, 35.00),
('IC2301', 1, 4, '2025-06-01 16:00:00', '2025-06-01 20:30:00', 'Intercités', 200, 180, 55.00),
('TGV9001', 1, 5, '2025-06-01 07:30:00', '2025-06-01 11:00:00', 'TGV', 320, 300, 75.00)
ON DUPLICATE KEY UPDATE id=id;

-- Insérer des paiements de test
INSERT INTO paiements (reservation_id, user_id, montant, methode_paiement, statut, numero_transaction, nom_titulaire, dernier_quatre_chiffres) VALUES
(1, 2, 91.00, 'CARTE_CREDIT', 'VALIDE', 'TXN-001-2025', 'Jean Dupont', '1234'),
(2, 2, 60.00, 'PAYPAL', 'VALIDE', 'TXN-002-2025', 'Jean Dupont', NULL),
(3, 3, 45.50, 'CARTE_CREDIT', 'EN_ATTENTE', 'TXN-003-2025', 'Marie Martin', '5678')
ON DUPLICATE KEY UPDATE id=id;

-- =====================================================
-- 8. VUES UTILES
-- =====================================================

-- Vue pour les voyages avec informations des gares
CREATE OR REPLACE VIEW vue_voyages_complets AS
SELECT 
    v.*,
    gd.nom as nom_gare_depart,
    gd.ville as ville_gare_depart,
    ga.nom as nom_gare_arrivee,
    ga.ville as ville_gare_arrivee,
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    TIMESTAMPDIFF(MINUTE, v.heure_depart, v.heure_arrivee) as duree_minutes,
    ROUND(((v.places_total - v.places_disponibles) / v.places_total) * 100, 2) as taux_occupation
FROM voyages v
JOIN gares gd ON v.gare_depart = gd.id
JOIN gares ga ON v.gare_arrivee = ga.id;

-- Vue pour les réservations avec détails complets
CREATE OR REPLACE VIEW vue_reservations_completes AS
SELECT 
    r.*,
    u.username,
    u.email as user_email,
    t.departure_city,
    t.destination_city,
    t.departure_time,
    t.arrival_time,
    t.price as trip_price,
    p.montant as montant_paye,
    p.statut as statut_paiement,
    p.methode_paiement
FROM reservations r
JOIN users u ON r.user_id = u.id
JOIN trips t ON r.trip_id = t.id
LEFT JOIN paiements p ON r.id = p.reservation_id;

-- Vue pour les statistiques admin
CREATE OR REPLACE VIEW vue_statistiques_admin AS
SELECT 
    (SELECT COUNT(*) FROM users WHERE role = 'user') as total_utilisateurs,
    (SELECT COUNT(*) FROM users WHERE role = 'user' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)) as nouveaux_utilisateurs_30j,
    (SELECT COUNT(*) FROM gares WHERE active = TRUE) as gares_actives,
    (SELECT COUNT(*) FROM voyages WHERE statut = 'PROGRAMME') as voyages_programmes,
    (SELECT COUNT(*) FROM reservations WHERE status = 'CONFIRMED') as reservations_confirmees,
    (SELECT COUNT(*) FROM demandes_annulation WHERE statut = 'EN_ATTENTE') as demandes_annulation_en_attente,
    (SELECT SUM(montant) FROM paiements WHERE statut = 'VALIDE' AND date_paiement >= DATE_SUB(NOW(), INTERVAL 30 DAY)) as revenus_30j,
    (SELECT COUNT(*) FROM paiements WHERE statut = 'EN_ATTENTE') as paiements_en_attente;

-- Afficher les statistiques
SELECT * FROM vue_statistiques_admin;
