package DAO;

import java.util.List;
import Modal.Gare;

public interface GareDAO {
    
    /**
     * Créer une nouvelle gare
     */
    boolean save(Gare gare);
    
    /**
     * Récupérer une gare par ID
     */
    Gare findById(int id);
    
    /**
     * Récupérer toutes les gares
     */
    List<Gare> findAll();
    
    /**
     * Récupérer les gares actives seulement
     */
    List<Gare> findAllActive();
    
    /**
     * Récupérer les gares par ville
     */
    List<Gare> findByVille(String ville);
    
    /**
     * Récupérer les gares par région
     */
    List<Gare> findByRegion(String region);
    
    /**
     * Rechercher des gares par nom ou ville
     */
    List<Gare> searchByNomOrVille(String searchTerm);
    
    /**
     * Mettre à jour une gare
     */
    boolean update(Gare gare);
    
    /**
     * Supprimer une gare
     */
    boolean delete(int id);
    
    /**
     * Activer/désactiver une gare
     */
    boolean toggleActive(int id);
    
    /**
     * Vérifier si une gare est utilisée dans des voyages
     */
    boolean isUsedInVoyages(int id);
    
    /**
     * Obtenir les statistiques d'une gare
     */
    GareStats getGareStats(int id);
    
    /**
     * Classe pour les statistiques d'une gare
     */
    public static class GareStats {
        private int nombreVoyagesDepart;
        private int nombreVoyagesArrivee;
        private int nombreReservationsActives;
        private double chiffreAffaires;
        
        public GareStats(int nombreVoyagesDepart, int nombreVoyagesArrivee, int nombreReservationsActives, double chiffreAffaires) {
            this.nombreVoyagesDepart = nombreVoyagesDepart;
            this.nombreVoyagesArrivee = nombreVoyagesArrivee;
            this.nombreReservationsActives = nombreReservationsActives;
            this.chiffreAffaires = chiffreAffaires;
        }
        
        // Getters
        public int getNombreVoyagesDepart() { return nombreVoyagesDepart; }
        public int getNombreVoyagesArrivee() { return nombreVoyagesArrivee; }
        public int getNombreReservationsActives() { return nombreReservationsActives; }
        public double getChiffreAffaires() { return chiffreAffaires; }
        public int getTotalVoyages() { return nombreVoyagesDepart + nombreVoyagesArrivee; }
    }
}
