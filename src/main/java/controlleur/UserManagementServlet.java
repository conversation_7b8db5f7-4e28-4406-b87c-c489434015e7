package controlleur;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import com.google.gson.Gson;
import com.google.gson.JsonObject;

import Modal.User;
import DAO.UserDAO;
import DAO.UserDAOImpl;
import service.UserService;

@WebServlet("/api/users")
public class UserManagementServlet extends HttpServlet {
    
    private UserService userService;
    private Gson gson;
    
    @Override
    public void init() throws ServletException {
        UserDAO userDAO = new UserDAOImpl();
        this.userService = new UserService(userDAO);
        this.gson = new Gson();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        // Vérifier que l'utilisateur est admin
        if (!isAdmin(request)) {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            return;
        }
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        String action = request.getParameter("action");
        
        try {
            if ("list".equals(action) || action == null) {
                // Récupérer tous les utilisateurs
                List<User> users = userService.getAllUsers();
                
                // Créer la réponse JSON
                JsonObject jsonResponse = new JsonObject();
                jsonResponse.addProperty("success", true);
                jsonResponse.addProperty("total", users.size());
                jsonResponse.add("users", gson.toJsonTree(users));
                
                PrintWriter out = response.getWriter();
                out.print(gson.toJson(jsonResponse));
                out.flush();
                
            } else if ("stats".equals(action)) {
                // Récupérer les statistiques des utilisateurs
                List<User> users = userService.getAllUsers();
                
                long totalUsers = users.size();
                long activeUsers = users.stream().filter(u -> "active".equals(u.getStatus())).count();
                long adminUsers = users.stream().filter(u -> "admin".equals(u.getRole())).count();
                long regularUsers = users.stream().filter(u -> "user".equals(u.getRole())).count();
                
                JsonObject stats = new JsonObject();
                stats.addProperty("totalUsers", totalUsers);
                stats.addProperty("activeUsers", activeUsers);
                stats.addProperty("adminUsers", adminUsers);
                stats.addProperty("regularUsers", regularUsers);
                
                JsonObject jsonResponse = new JsonObject();
                jsonResponse.addProperty("success", true);
                jsonResponse.add("stats", stats);
                
                PrintWriter out = response.getWriter();
                out.print(gson.toJson(jsonResponse));
                out.flush();
                
            } else {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                JsonObject error = new JsonObject();
                error.addProperty("success", false);
                error.addProperty("message", "Action non reconnue");
                
                PrintWriter out = response.getWriter();
                out.print(gson.toJson(error));
                out.flush();
            }
            
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            JsonObject error = new JsonObject();
            error.addProperty("success", false);
            error.addProperty("message", "Erreur serveur : " + e.getMessage());
            
            PrintWriter out = response.getWriter();
            out.print(gson.toJson(error));
            out.flush();
            
            e.printStackTrace();
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        // Vérifier que l'utilisateur est admin
        if (!isAdmin(request)) {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            return;
        }
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        String action = request.getParameter("action");
        
        try {
            if ("delete".equals(action)) {
                // Supprimer un utilisateur
                String userIdStr = request.getParameter("userId");
                if (userIdStr == null || userIdStr.trim().isEmpty()) {
                    response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                    return;
                }
                
                int userId = Integer.parseInt(userIdStr);
                
                // Empêcher la suppression de l'admin principal
                if (userId == 1) {
                    JsonObject error = new JsonObject();
                    error.addProperty("success", false);
                    error.addProperty("message", "Impossible de supprimer l'administrateur principal");
                    
                    PrintWriter out = response.getWriter();
                    out.print(gson.toJson(error));
                    out.flush();
                    return;
                }
                
                userService.deleteUser(userId);
                
                JsonObject jsonResponse = new JsonObject();
                jsonResponse.addProperty("success", true);
                jsonResponse.addProperty("message", "Utilisateur supprimé avec succès");
                
                PrintWriter out = response.getWriter();
                out.print(gson.toJson(jsonResponse));
                out.flush();
                
            } else if ("toggle-status".equals(action)) {
                // Changer le statut d'un utilisateur
                String userIdStr = request.getParameter("userId");
                if (userIdStr == null || userIdStr.trim().isEmpty()) {
                    response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                    return;
                }
                
                int userId = Integer.parseInt(userIdStr);
                User user = userService.getUserById(userId);
                
                if (user != null) {
                    String newStatus = "active".equals(user.getStatus()) ? "inactive" : "active";
                    user.setStatus(newStatus);
                    userService.updateUser(user);
                    
                    JsonObject jsonResponse = new JsonObject();
                    jsonResponse.addProperty("success", true);
                    jsonResponse.addProperty("message", "Statut mis à jour avec succès");
                    jsonResponse.addProperty("newStatus", newStatus);
                    
                    PrintWriter out = response.getWriter();
                    out.print(gson.toJson(jsonResponse));
                    out.flush();
                } else {
                    response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                }
                
            } else {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            }
            
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            JsonObject error = new JsonObject();
            error.addProperty("success", false);
            error.addProperty("message", "Erreur serveur : " + e.getMessage());
            
            PrintWriter out = response.getWriter();
            out.print(gson.toJson(error));
            out.flush();
            
            e.printStackTrace();
        }
    }
    
    /**
     * Vérifier si l'utilisateur connecté est un administrateur
     */
    private boolean isAdmin(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            return false;
        }
        
        User user = (User) session.getAttribute("user");
        return user != null && "admin".equals(user.getRole());
    }
}
