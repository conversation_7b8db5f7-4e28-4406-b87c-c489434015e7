package controlleur;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonSerializer;

import Modal.User;
import Modal.Trip;
import DAO.*;
import service.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@WebServlet("/api/admin/*")
public class AdminServlet extends HttpServlet {
    
    private UserDAO userDAO;
    private TripDAO tripDAO;
    private Gson gson;
    
    @Override
    public void init() throws ServletException {
        this.userDAO = new UserDAOImpl();
        this.tripDAO = new TripDAOImpl();
        
        // Configuration Gson pour gérer LocalDateTime
        this.gson = new GsonBuilder()
            .registerTypeAdapter(LocalDateTime.class, (JsonSerializer<LocalDateTime>) (src, typeOfSrc, context) -> 
                context.serialize(src.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))))
            .create();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        // Vérifier que l'utilisateur est admin
        User user = getLoggedInUser(request);
        if (user == null || !"admin".equals(user.getRole())) {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            return;
        }
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        String pathInfo = request.getPathInfo();
        String action = request.getParameter("action");
        
        try {
            if ("/users".equals(pathInfo)) {
                handleUsersRequest(request, response, action);
            } else if ("/trips".equals(pathInfo)) {
                handleTripsRequest(request, response, action);
            } else if ("/stats".equals(pathInfo)) {
                handleStatsRequest(request, response, action);
            } else if ("/dashboard".equals(pathInfo)) {
                handleDashboardRequest(request, response, action);
            } else {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                JsonObject error = new JsonObject();
                error.addProperty("success", false);
                error.addProperty("message", "Endpoint non trouvé");
                
                PrintWriter out = response.getWriter();
                out.print(gson.toJson(error));
                out.flush();
            }
            
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            JsonObject error = new JsonObject();
            error.addProperty("success", false);
            error.addProperty("message", "Erreur serveur : " + e.getMessage());
            
            PrintWriter out = response.getWriter();
            out.print(gson.toJson(error));
            out.flush();
            
            e.printStackTrace();
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        // Vérifier que l'utilisateur est admin
        User user = getLoggedInUser(request);
        if (user == null || !"admin".equals(user.getRole())) {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            return;
        }
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        String pathInfo = request.getPathInfo();
        String action = request.getParameter("action");
        
        try {
            if ("/users".equals(pathInfo)) {
                handleUsersPostRequest(request, response, action);
            } else if ("/trips".equals(pathInfo)) {
                handleTripsPostRequest(request, response, action);
            } else {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                JsonObject error = new JsonObject();
                error.addProperty("success", false);
                error.addProperty("message", "Endpoint non trouvé");
                
                PrintWriter out = response.getWriter();
                out.print(gson.toJson(error));
                out.flush();
            }
            
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            JsonObject error = new JsonObject();
            error.addProperty("success", false);
            error.addProperty("message", "Erreur serveur : " + e.getMessage());
            
            PrintWriter out = response.getWriter();
            out.print(gson.toJson(error));
            out.flush();
            
            e.printStackTrace();
        }
    }
    
    /**
     * Gestion des requêtes GET pour les utilisateurs
     */
    private void handleUsersRequest(HttpServletRequest request, HttpServletResponse response, String action) 
            throws IOException {
        
        PrintWriter out = response.getWriter();
        
        if ("list".equals(action) || action == null) {
            // Récupérer tous les utilisateurs
            List<User> users = userDAO.findAll();
            
            JsonObject jsonResponse = new JsonObject();
            jsonResponse.addProperty("success", true);
            jsonResponse.addProperty("total", users.size());
            jsonResponse.add("users", gson.toJsonTree(users));
            
            out.print(gson.toJson(jsonResponse));
            
        } else if ("stats".equals(action)) {
            // Statistiques des utilisateurs
            List<User> allUsers = userDAO.findAll();
            long totalUsers = allUsers.size();
            long activeUsers = allUsers.stream().filter(u -> "user".equals(u.getRole())).count();
            long adminUsers = allUsers.stream().filter(u -> "admin".equals(u.getRole())).count();
            
            JsonObject jsonResponse = new JsonObject();
            jsonResponse.addProperty("success", true);
            jsonResponse.addProperty("totalUsers", totalUsers);
            jsonResponse.addProperty("activeUsers", activeUsers);
            jsonResponse.addProperty("adminUsers", adminUsers);
            
            out.print(gson.toJson(jsonResponse));
            
        } else {
            JsonObject error = new JsonObject();
            error.addProperty("success", false);
            error.addProperty("message", "Action non reconnue pour les utilisateurs");
            out.print(gson.toJson(error));
        }
        
        out.flush();
    }
    
    /**
     * Gestion des requêtes GET pour les trips
     */
    private void handleTripsRequest(HttpServletRequest request, HttpServletResponse response, String action) 
            throws IOException {
        
        PrintWriter out = response.getWriter();
        
        if ("list".equals(action) || action == null) {
            // Récupérer tous les trips (utiliser searchTrips avec paramètres vides)
            List<Modal.Trip> trips = tripDAO.searchTrips("", "", null);

            JsonObject jsonResponse = new JsonObject();
            jsonResponse.addProperty("success", true);
            jsonResponse.addProperty("total", trips.size());
            jsonResponse.add("trips", gson.toJsonTree(trips));

            out.print(gson.toJson(jsonResponse));

        } else if ("stats".equals(action)) {
            // Statistiques des trips
            List<Modal.Trip> allTrips = tripDAO.searchTrips("", "", null);
            int totalTrips = allTrips.size();
            int totalSeats = allTrips.stream().mapToInt(Modal.Trip::getAvailableSeats).sum();
            double averagePrice = allTrips.stream().mapToDouble(Modal.Trip::getPrice).average().orElse(0.0);
            
            JsonObject jsonResponse = new JsonObject();
            jsonResponse.addProperty("success", true);
            jsonResponse.addProperty("totalTrips", totalTrips);
            jsonResponse.addProperty("totalSeats", totalSeats);
            jsonResponse.addProperty("averagePrice", Math.round(averagePrice * 100.0) / 100.0);
            
            out.print(gson.toJson(jsonResponse));
            
        } else {
            JsonObject error = new JsonObject();
            error.addProperty("success", false);
            error.addProperty("message", "Action non reconnue pour les trips");
            out.print(gson.toJson(error));
        }
        
        out.flush();
    }
    

    
    /**
     * Gestion des requêtes GET pour les statistiques générales
     */
    private void handleStatsRequest(HttpServletRequest request, HttpServletResponse response, String action)
            throws IOException {

        PrintWriter out = response.getWriter();

        // Récupérer toutes les statistiques pour le dashboard
        List<User> allUsers = userDAO.findAll();
        List<Modal.Trip> allTrips = tripDAO.searchTrips("", "", null);

        // Calculer les statistiques
        JsonObject stats = new JsonObject();
        stats.addProperty("success", true);

        // Statistiques utilisateurs
        stats.addProperty("totalUsers", allUsers.size());
        stats.addProperty("activeUsers", allUsers.stream().filter(u -> "user".equals(u.getRole())).count());

        // Statistiques trips
        stats.addProperty("totalTrips", allTrips.size());
        stats.addProperty("totalSeats", allTrips.stream().mapToInt(Modal.Trip::getAvailableSeats).sum());

        // Statistiques réservations (valeurs par défaut pour l'instant)
        stats.addProperty("totalReservations", 0);
        stats.addProperty("confirmedReservations", 0);
        stats.addProperty("pendingCancellations", 0);

        // Revenus (valeur par défaut pour l'instant)
        stats.addProperty("totalRevenue", 0.0);

        out.print(gson.toJson(stats));
        out.flush();
    }
    
    /**
     * Gestion des requêtes GET pour le dashboard
     */
    private void handleDashboardRequest(HttpServletRequest request, HttpServletResponse response, String action) 
            throws IOException {
        
        // Rediriger vers les statistiques générales
        handleStatsRequest(request, response, action);
    }
    
    /**
     * Gestion des requêtes POST pour les utilisateurs
     */
    private void handleUsersPostRequest(HttpServletRequest request, HttpServletResponse response, String action) 
            throws IOException {
        
        PrintWriter out = response.getWriter();
        JsonObject jsonResponse = new JsonObject();
        
        if ("toggle-status".equals(action)) {
            // Activer/désactiver un utilisateur (à implémenter selon vos besoins)
            String userIdStr = request.getParameter("userId");
            if (userIdStr != null) {
                // Logique pour bloquer/débloquer un utilisateur
                jsonResponse.addProperty("success", true);
                jsonResponse.addProperty("message", "Statut utilisateur modifié");
            } else {
                jsonResponse.addProperty("success", false);
                jsonResponse.addProperty("message", "ID utilisateur manquant");
            }
        } else {
            jsonResponse.addProperty("success", false);
            jsonResponse.addProperty("message", "Action non reconnue");
        }
        
        out.print(gson.toJson(jsonResponse));
        out.flush();
    }
    
    /**
     * Gestion des requêtes POST pour les trips
     */
    private void handleTripsPostRequest(HttpServletRequest request, HttpServletResponse response, String action) 
            throws IOException {
        
        PrintWriter out = response.getWriter();
        JsonObject jsonResponse = new JsonObject();
        
        // Déléguer au TripManagementServlet existant
        jsonResponse.addProperty("success", false);
        jsonResponse.addProperty("message", "Utiliser /api/trips-management pour la gestion des trips");
        
        out.print(gson.toJson(jsonResponse));
        out.flush();
    }
    

    
    /**
     * Récupérer l'utilisateur connecté
     */
    private User getLoggedInUser(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            return null;
        }
        return (User) session.getAttribute("user");
    }
}
