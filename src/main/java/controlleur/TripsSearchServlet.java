package controlleur;

import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSerializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonSerializationContext;

import Modal.Trip;
import utils.DatabaseConnection;

@WebServlet("/api/trips-search")
public class TripsSearchServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Configuration de la réponse
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);
        
        PrintWriter out = response.getWriter();
        
        try {
            String action = request.getParameter("action");
            
            if ("search".equals(action)) {
                handleSearchTrips(request, response, out);
            } else {
                sendErrorResponse(out, "Action non supportée");
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            sendErrorResponse(out, "Erreur serveur: " + e.getMessage());
        }
    }
    
    private void handleSearchTrips(HttpServletRequest request, HttpServletResponse response, PrintWriter out) {
        try {
            // Récupération des paramètres
            String departureCity = request.getParameter("departureCity");
            String destinationCity = request.getParameter("destinationCity");
            String travelDate = request.getParameter("travelDate");
            
            // Validation des paramètres
            if (departureCity == null || departureCity.trim().isEmpty() ||
                destinationCity == null || destinationCity.trim().isEmpty() ||
                travelDate == null || travelDate.trim().isEmpty()) {
                sendErrorResponse(out, "Tous les paramètres sont requis");
                return;
            }
            
            // Recherche des voyages
            List<Trip> trips = searchTrips(departureCity.trim(), destinationCity.trim(), travelDate);
            
            // Création de la réponse
            SearchResponse searchResponse = new SearchResponse();
            searchResponse.success = true;
            searchResponse.message = trips.isEmpty() ? "Aucun voyage trouvé" : trips.size() + " voyage(s) trouvé(s)";
            searchResponse.data = trips;
            searchResponse.searchCriteria = new SearchCriteria(departureCity, destinationCity, travelDate);
            
            // Sérialisation JSON avec gestion des LocalDateTime
            Gson gson = new GsonBuilder()
                .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeSerializer())
                .setPrettyPrinting()
                .create();
            
            out.print(gson.toJson(searchResponse));
            
        } catch (Exception e) {
            e.printStackTrace();
            sendErrorResponse(out, "Erreur lors de la recherche: " + e.getMessage());
        }
    }
    
    private List<Trip> searchTrips(String departureCity, String destinationCity, String travelDate) 
            throws SQLException {
        List<Trip> trips = new ArrayList<>();
        
        String sql = "SELECT * FROM trips WHERE " +
                    "LOWER(departure_city) = LOWER(?) AND " +
                    "LOWER(destination_city) = LOWER(?) AND " +
                    "DATE(departure_time) = ? " +
                    "ORDER BY departure_time";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, departureCity);
            pstmt.setString(2, destinationCity);
            pstmt.setString(3, travelDate);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    Trip trip = new Trip();
                    trip.setId(rs.getInt("id"));
                    trip.setDepartureCity(rs.getString("departure_city"));
                    trip.setDestinationCity(rs.getString("destination_city"));
                    
                    // Conversion Timestamp vers LocalDateTime
                    Timestamp departureTimestamp = rs.getTimestamp("departure_time");
                    if (departureTimestamp != null) {
                        trip.setDepartureTime(departureTimestamp.toLocalDateTime());
                    }
                    
                    Timestamp arrivalTimestamp = rs.getTimestamp("arrival_time");
                    if (arrivalTimestamp != null) {
                        trip.setArrivalTime(arrivalTimestamp.toLocalDateTime());
                    }
                    
                    trip.setPrice(rs.getDouble("price"));
                    trip.setAvailableSeats(rs.getInt("available_seats"));
                    
                    trips.add(trip);
                }
            }
        }
        
        return trips;
    }
    
    private void sendErrorResponse(PrintWriter out, String message) {
        SearchResponse errorResponse = new SearchResponse();
        errorResponse.success = false;
        errorResponse.message = message;
        errorResponse.data = new ArrayList<>();
        
        Gson gson = new Gson();
        out.print(gson.toJson(errorResponse));
    }
    
    // Classes pour la réponse JSON
    private static class SearchResponse {
        boolean success;
        String message;
        List<Trip> data;
        SearchCriteria searchCriteria;
    }
    
    private static class SearchCriteria {
        String departureCity;
        String destinationCity;
        String travelDate;
        
        SearchCriteria(String departureCity, String destinationCity, String travelDate) {
            this.departureCity = departureCity;
            this.destinationCity = destinationCity;
            this.travelDate = travelDate;
        }
    }
    
    // Sérialiseur pour LocalDateTime
    private static class LocalDateTimeSerializer implements JsonSerializer<LocalDateTime> {
        private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        @Override
        public JsonElement serialize(LocalDateTime localDateTime, java.lang.reflect.Type srcType, 
                                   JsonSerializationContext context) {
            return context.serialize(localDateTime.format(formatter));
        }
    }
}
