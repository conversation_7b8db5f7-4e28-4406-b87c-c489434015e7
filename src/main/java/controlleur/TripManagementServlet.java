package controlleur;

import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonSerializer;

import Modal.Trip;
import Modal.User;
import DAO.TripDAO;
import DAO.TripDAOImpl;
import service.TripService;

@WebServlet("/api/trips-management")
public class TripManagementServlet extends HttpServlet {
    
    private TripService tripService;
    private Gson gson;
    
    @Override
    public void init() throws ServletException {
        TripDAO tripDAO = new TripDAOImpl();
        this.tripService = new TripService(tripDAO);
        
        // Configuration Gson pour gérer LocalDateTime
        this.gson = new GsonBuilder()
            .registerTypeAdapter(LocalDateTime.class, (JsonSerializer<LocalDateTime>) (src, typeOfSrc, context) -> 
                context.serialize(src.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))))
            .create();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        // Vérifier que l'utilisateur est admin
        if (!isAdmin(request)) {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            return;
        }
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        String action = request.getParameter("action");
        
        try {
            if ("list".equals(action) || action == null) {
                // Récupérer tous les trips
                List<Trip> trips = tripService.getAllTrips();
                
                // Créer la réponse JSON
                JsonObject jsonResponse = new JsonObject();
                jsonResponse.addProperty("success", true);
                jsonResponse.addProperty("total", trips.size());
                jsonResponse.add("trips", gson.toJsonTree(trips));
                
                PrintWriter out = response.getWriter();
                out.print(gson.toJson(jsonResponse));
                out.flush();
                
            } else if ("stats".equals(action)) {
                // Récupérer les statistiques des trips
                List<Trip> trips = tripService.getAllTrips();
                
                long totalTrips = trips.size();
                long availableTrips = trips.stream().filter(t -> t.getAvailableSeats() > 0).count();
                long fullTrips = trips.stream().filter(t -> t.getAvailableSeats() == 0).count();
                double averagePrice = trips.stream().mapToDouble(Trip::getPrice).average().orElse(0.0);
                int totalSeats = trips.stream().mapToInt(Trip::getAvailableSeats).sum();
                
                JsonObject stats = new JsonObject();
                stats.addProperty("totalTrips", totalTrips);
                stats.addProperty("availableTrips", availableTrips);
                stats.addProperty("fullTrips", fullTrips);
                stats.addProperty("averagePrice", Math.round(averagePrice * 100.0) / 100.0);
                stats.addProperty("totalSeats", totalSeats);
                
                JsonObject jsonResponse = new JsonObject();
                jsonResponse.addProperty("success", true);
                jsonResponse.add("stats", stats);
                
                PrintWriter out = response.getWriter();
                out.print(gson.toJson(jsonResponse));
                out.flush();
                
            } else if ("get".equals(action)) {
                // Récupérer un trip spécifique
                String tripIdStr = request.getParameter("id");
                if (tripIdStr == null || tripIdStr.trim().isEmpty()) {
                    response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                    return;
                }
                
                int tripId = Integer.parseInt(tripIdStr);
                Trip trip = tripService.getTripById(tripId);
                
                JsonObject jsonResponse = new JsonObject();
                if (trip != null) {
                    jsonResponse.addProperty("success", true);
                    jsonResponse.add("trip", gson.toJsonTree(trip));
                } else {
                    jsonResponse.addProperty("success", false);
                    jsonResponse.addProperty("message", "Trip non trouvé");
                }
                
                PrintWriter out = response.getWriter();
                out.print(gson.toJson(jsonResponse));
                out.flush();
                
            } else {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                JsonObject error = new JsonObject();
                error.addProperty("success", false);
                error.addProperty("message", "Action non reconnue");
                
                PrintWriter out = response.getWriter();
                out.print(gson.toJson(error));
                out.flush();
            }
            
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            JsonObject error = new JsonObject();
            error.addProperty("success", false);
            error.addProperty("message", "Erreur serveur : " + e.getMessage());
            
            PrintWriter out = response.getWriter();
            out.print(gson.toJson(error));
            out.flush();
            
            e.printStackTrace();
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        // Vérifier que l'utilisateur est admin
        if (!isAdmin(request)) {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            return;
        }
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        String action = request.getParameter("action");
        
        try {
            if ("delete".equals(action)) {
                // Supprimer un trip
                String tripIdStr = request.getParameter("tripId");
                if (tripIdStr == null || tripIdStr.trim().isEmpty()) {
                    response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                    return;
                }
                
                int tripId = Integer.parseInt(tripIdStr);
                boolean deleted = tripService.deleteTrip(tripId);
                
                JsonObject jsonResponse = new JsonObject();
                jsonResponse.addProperty("success", deleted);
                jsonResponse.addProperty("message", deleted ? "Trip supprimé avec succès" : "Erreur lors de la suppression");
                
                PrintWriter out = response.getWriter();
                out.print(gson.toJson(jsonResponse));
                out.flush();
                
            } else if ("create".equals(action)) {
                // Créer un nouveau trip
                Trip trip = createTripFromRequest(request);
                if (trip == null) {
                    JsonObject error = new JsonObject();
                    error.addProperty("success", false);
                    error.addProperty("message", "Données invalides");
                    
                    PrintWriter out = response.getWriter();
                    out.print(gson.toJson(error));
                    out.flush();
                    return;
                }
                
                boolean created = tripService.createTrip(trip);
                
                JsonObject jsonResponse = new JsonObject();
                jsonResponse.addProperty("success", created);
                jsonResponse.addProperty("message", created ? "Trip créé avec succès" : "Erreur lors de la création");
                
                PrintWriter out = response.getWriter();
                out.print(gson.toJson(jsonResponse));
                out.flush();
                
            } else if ("update".equals(action)) {
                // Mettre à jour un trip
                Trip trip = createTripFromRequest(request);
                if (trip == null) {
                    JsonObject error = new JsonObject();
                    error.addProperty("success", false);
                    error.addProperty("message", "Données invalides");
                    
                    PrintWriter out = response.getWriter();
                    out.print(gson.toJson(error));
                    out.flush();
                    return;
                }
                
                boolean updated = tripService.updateTrip(trip);
                
                JsonObject jsonResponse = new JsonObject();
                jsonResponse.addProperty("success", updated);
                jsonResponse.addProperty("message", updated ? "Trip mis à jour avec succès" : "Erreur lors de la mise à jour");
                
                PrintWriter out = response.getWriter();
                out.print(gson.toJson(jsonResponse));
                out.flush();
                
            } else {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            }
            
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            JsonObject error = new JsonObject();
            error.addProperty("success", false);
            error.addProperty("message", "Erreur serveur : " + e.getMessage());
            
            PrintWriter out = response.getWriter();
            out.print(gson.toJson(error));
            out.flush();
            
            e.printStackTrace();
        }
    }
    
    /**
     * Créer un objet Trip à partir des paramètres de la requête
     */
    private Trip createTripFromRequest(HttpServletRequest request) {
        try {
            Trip trip = new Trip();
            
            String idStr = request.getParameter("id");
            if (idStr != null && !idStr.trim().isEmpty()) {
                trip.setId(Integer.parseInt(idStr));
            }
            
            trip.setDepartureCity(request.getParameter("departureCity"));
            trip.setDestinationCity(request.getParameter("destinationCity"));
            
            String departureTimeStr = request.getParameter("departureTime");
            String arrivalTimeStr = request.getParameter("arrivalTime");
            
            if (departureTimeStr != null && arrivalTimeStr != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm");
                trip.setDepartureTime(LocalDateTime.parse(departureTimeStr, formatter));
                trip.setArrivalTime(LocalDateTime.parse(arrivalTimeStr, formatter));
            }
            
            String priceStr = request.getParameter("price");
            if (priceStr != null) {
                trip.setPrice(Double.parseDouble(priceStr));
            }
            
            String seatsStr = request.getParameter("availableSeats");
            if (seatsStr != null) {
                trip.setAvailableSeats(Integer.parseInt(seatsStr));
            }
            
            return trip;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * Vérifier si l'utilisateur connecté est un administrateur
     */
    private boolean isAdmin(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            return false;
        }
        
        User user = (User) session.getAttribute("user");
        return user != null && "admin".equals(user.getRole());
    }
}
