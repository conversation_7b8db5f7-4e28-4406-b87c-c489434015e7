package controlleur;

import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSerializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonSerializationContext;

import Modal.Trip;
import utils.DatabaseConnection;

@WebServlet("/api/all-trips")
public class AllTripsServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Configuration de la réponse
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);
        
        PrintWriter out = response.getWriter();
        
        try {
            String action = request.getParameter("action");
            
            if ("getAll".equals(action)) {
                handleGetAllTrips(request, response, out);
            } else if ("getByDate".equals(action)) {
                handleGetTripsByDate(request, response, out);
            } else {
                // Par défaut, récupérer tous les voyages
                handleGetAllTrips(request, response, out);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            sendErrorResponse(out, "Erreur serveur: " + e.getMessage());
        }
    }
    
    private void handleGetAllTrips(HttpServletRequest request, HttpServletResponse response, PrintWriter out) {
        try {
            // Récupération des paramètres optionnels
            String sortBy = request.getParameter("sortBy"); // departure_time, price, destination_city
            String order = request.getParameter("order"); // asc, desc
            String limit = request.getParameter("limit");
            
            // Valeurs par défaut
            if (sortBy == null || sortBy.trim().isEmpty()) {
                sortBy = "departure_time";
            }
            if (order == null || order.trim().isEmpty()) {
                order = "asc";
            }
            
            // Récupération des voyages
            List<Trip> trips = getAllTrips(sortBy, order, limit);
            
            // Création de la réponse
            AllTripsResponse tripsResponse = new AllTripsResponse();
            tripsResponse.success = true;
            tripsResponse.message = trips.isEmpty() ? "Aucun voyage disponible" : trips.size() + " voyage(s) disponible(s)";
            tripsResponse.data = trips;
            tripsResponse.total = trips.size();
            
            // Sérialisation JSON avec gestion des LocalDateTime
            Gson gson = new GsonBuilder()
                .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeSerializer())
                .setPrettyPrinting()
                .create();
            
            out.print(gson.toJson(tripsResponse));
            
        } catch (Exception e) {
            e.printStackTrace();
            sendErrorResponse(out, "Erreur lors de la récupération des voyages: " + e.getMessage());
        }
    }
    
    private void handleGetTripsByDate(HttpServletRequest request, HttpServletResponse response, PrintWriter out) {
        try {
            String dateFilter = request.getParameter("date"); // today, tomorrow, week
            
            if (dateFilter == null || dateFilter.trim().isEmpty()) {
                dateFilter = "all";
            }
            
            List<Trip> trips = getTripsByDateFilter(dateFilter);
            
            AllTripsResponse tripsResponse = new AllTripsResponse();
            tripsResponse.success = true;
            tripsResponse.message = trips.size() + " voyage(s) trouvé(s) pour: " + dateFilter;
            tripsResponse.data = trips;
            tripsResponse.total = trips.size();
            tripsResponse.filter = dateFilter;
            
            Gson gson = new GsonBuilder()
                .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeSerializer())
                .setPrettyPrinting()
                .create();
            
            out.print(gson.toJson(tripsResponse));
            
        } catch (Exception e) {
            e.printStackTrace();
            sendErrorResponse(out, "Erreur lors du filtrage par date: " + e.getMessage());
        }
    }
    
    private List<Trip> getAllTrips(String sortBy, String order, String limit) throws SQLException {
        List<Trip> trips = new ArrayList<>();
        
        // Validation des paramètres pour éviter l'injection SQL
        if (!isValidSortColumn(sortBy)) {
            sortBy = "departure_time";
        }
        if (!order.equalsIgnoreCase("asc") && !order.equalsIgnoreCase("desc")) {
            order = "asc";
        }
        
        String sql = "SELECT * FROM trips ORDER BY " + sortBy + " " + order.toUpperCase();
        
        // Ajouter une limite si spécifiée
        if (limit != null && !limit.trim().isEmpty()) {
            try {
                int limitValue = Integer.parseInt(limit);
                if (limitValue > 0 && limitValue <= 1000) { // Limite maximale de sécurité
                    sql += " LIMIT " + limitValue;
                }
            } catch (NumberFormatException e) {
                // Ignorer la limite si elle n'est pas valide
            }
        }
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    Trip trip = createTripFromResultSet(rs);
                    trips.add(trip);
                }
            }
        }
        
        return trips;
    }
    
    private List<Trip> getTripsByDateFilter(String dateFilter) throws SQLException {
        List<Trip> trips = new ArrayList<>();
        String sql;
        
        switch (dateFilter.toLowerCase()) {
            case "today":
                sql = "SELECT * FROM trips WHERE DATE(departure_time) = CURDATE() ORDER BY departure_time";
                break;
            case "tomorrow":
                sql = "SELECT * FROM trips WHERE DATE(departure_time) = DATE_ADD(CURDATE(), INTERVAL 1 DAY) ORDER BY departure_time";
                break;
            case "week":
                sql = "SELECT * FROM trips WHERE DATE(departure_time) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) ORDER BY departure_time";
                break;
            case "future":
                sql = "SELECT * FROM trips WHERE departure_time >= NOW() ORDER BY departure_time";
                break;
            default:
                sql = "SELECT * FROM trips ORDER BY departure_time";
                break;
        }
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    Trip trip = createTripFromResultSet(rs);
                    trips.add(trip);
                }
            }
        }
        
        return trips;
    }
    
    private Trip createTripFromResultSet(ResultSet rs) throws SQLException {
        Trip trip = new Trip();
        trip.setId(rs.getInt("id"));
        trip.setDepartureCity(rs.getString("departure_city"));
        trip.setDestinationCity(rs.getString("destination_city"));
        
        // Conversion Timestamp vers LocalDateTime
        Timestamp departureTimestamp = rs.getTimestamp("departure_time");
        if (departureTimestamp != null) {
            trip.setDepartureTime(departureTimestamp.toLocalDateTime());
        }
        
        Timestamp arrivalTimestamp = rs.getTimestamp("arrival_time");
        if (arrivalTimestamp != null) {
            trip.setArrivalTime(arrivalTimestamp.toLocalDateTime());
        }
        
        trip.setPrice(rs.getDouble("price"));
        trip.setAvailableSeats(rs.getInt("available_seats"));
        
        return trip;
    }
    
    private boolean isValidSortColumn(String column) {
        String[] validColumns = {"id", "departure_city", "destination_city", "departure_time", "arrival_time", "price", "available_seats"};
        for (String validColumn : validColumns) {
            if (validColumn.equals(column)) {
                return true;
            }
        }
        return false;
    }
    
    private void sendErrorResponse(PrintWriter out, String message) {
        AllTripsResponse errorResponse = new AllTripsResponse();
        errorResponse.success = false;
        errorResponse.message = message;
        errorResponse.data = new ArrayList<>();
        errorResponse.total = 0;
        
        Gson gson = new Gson();
        out.print(gson.toJson(errorResponse));
    }
    
    // Classes pour la réponse JSON
    private static class AllTripsResponse {
        boolean success;
        String message;
        List<Trip> data;
        int total;
        String filter;
    }
    
    // Sérialiseur pour LocalDateTime
    private static class LocalDateTimeSerializer implements JsonSerializer<LocalDateTime> {
        private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        @Override
        public JsonElement serialize(LocalDateTime localDateTime, java.lang.reflect.Type srcType, 
                                   JsonSerializationContext context) {
            return context.serialize(localDateTime.format(formatter));
        }
    }
}
