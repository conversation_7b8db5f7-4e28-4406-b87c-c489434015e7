package service;

import java.time.LocalDate;
import java.util.List;

import DAO.TripDAO;
import Modal.Trip;

public class TripService {
    private TripDAO tripDAO;
    
    public TripService(TripDAO tripDAO) {
        this.tripDAO = tripDAO;
    }
    
    // Récupérer tous les trips
    public List<Trip> getAllTrips() {
        return tripDAO.findAll();
    }
    
    // Rechercher des trips par critères
    public List<Trip> searchTrips(String departureCity, String destinationCity, LocalDate date) {
        return tripDAO.searchTrips(departureCity, destinationCity, date);
    }
    
    // Récupérer un trip par ID
    public Trip getTripById(int id) {
        return tripDAO.findById(id);
    }
    
    // Créer un nouveau trip
    public boolean createTrip(Trip trip) {
        // Validation des données
        if (trip == null) {
            return false;
        }
        
        if (trip.getDepartureCity() == null || trip.getDepartureCity().trim().isEmpty()) {
            return false;
        }
        
        if (trip.getDestinationCity() == null || trip.getDestinationCity().trim().isEmpty()) {
            return false;
        }
        
        if (trip.getDepartureTime() == null || trip.getArrivalTime() == null) {
            return false;
        }
        
        if (trip.getDepartureTime().isAfter(trip.getArrivalTime())) {
            return false;
        }
        
        if (trip.getPrice() <= 0) {
            return false;
        }
        
        if (trip.getAvailableSeats() < 0) {
            return false;
        }
        
        return tripDAO.save(trip);
    }
    
    // Mettre à jour un trip
    public boolean updateTrip(Trip trip) {
        // Validation des données
        if (trip == null || trip.getId() <= 0) {
            return false;
        }
        
        if (trip.getDepartureCity() == null || trip.getDepartureCity().trim().isEmpty()) {
            return false;
        }
        
        if (trip.getDestinationCity() == null || trip.getDestinationCity().trim().isEmpty()) {
            return false;
        }
        
        if (trip.getDepartureTime() == null || trip.getArrivalTime() == null) {
            return false;
        }
        
        if (trip.getDepartureTime().isAfter(trip.getArrivalTime())) {
            return false;
        }
        
        if (trip.getPrice() <= 0) {
            return false;
        }
        
        if (trip.getAvailableSeats() < 0) {
            return false;
        }
        
        return tripDAO.update(trip);
    }
    
    // Supprimer un trip
    public boolean deleteTrip(int id) {
        if (id <= 0) {
            return false;
        }
        return tripDAO.delete(id);
    }
    
    // Vérifier la disponibilité d'un trip
    public boolean isTripAvailable(int tripId) {
        Trip trip = getTripById(tripId);
        return trip != null && trip.getAvailableSeats() > 0;
    }
    
    // Réserver des places (diminuer le nombre de places disponibles)
    public boolean reserveSeats(int tripId, int numberOfSeats) {
        Trip trip = getTripById(tripId);
        if (trip == null || trip.getAvailableSeats() < numberOfSeats) {
            return false;
        }
        
        trip.setAvailableSeats(trip.getAvailableSeats() - numberOfSeats);
        return updateTrip(trip);
    }
    
    // Annuler une réservation (augmenter le nombre de places disponibles)
    public boolean cancelReservation(int tripId, int numberOfSeats) {
        Trip trip = getTripById(tripId);
        if (trip == null) {
            return false;
        }
        
        trip.setAvailableSeats(trip.getAvailableSeats() + numberOfSeats);
        return updateTrip(trip);
    }
}
