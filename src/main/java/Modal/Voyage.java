package Modal;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.List;

public class Voyage {
    private int id;
    private String numeroVoyage;
    private int gareDepart;
    private int gareArrivee;
    private LocalDateTime heureDepart;
    private LocalDateTime heureArrivee;
    private String typeTrain; // TGV, TER, Intercités, etc.
    private int placesTotal;
    private int placesDisponibles;
    private double prixBase;
    private String statut; // PROGRAMME, EN_COURS, TERMINE, ANNULE, RETARDE
    private String motifAnnulation;
    private int retardMinutes;
    private String voie;
    private boolean reservationOuverte;
    private LocalDateTime dateCreation;
    private LocalDateTime dateModification;
    
    // Informations des gares (pour l'affichage)
    private String nomGareDepart;
    private String villeGareDepart;
    private String nomGareArrivee;
    private String villeGareArrivee;
    
    // Constructeurs
    public Voyage() {
        this.dateCreation = LocalDateTime.now();
        this.dateModification = LocalDateTime.now();
        this.statut = "PROGRAMME";
        this.reservationOuverte = true;
        this.retardMinutes = 0;
    }
    
    public Voyage(String numeroVoyage, int gareDepart, int gareArrivee, LocalDateTime heureDepart, LocalDateTime heureArrivee) {
        this();
        this.numeroVoyage = numeroVoyage;
        this.gareDepart = gareDepart;
        this.gareArrivee = gareArrivee;
        this.heureDepart = heureDepart;
        this.heureArrivee = heureArrivee;
    }
    
    // Getters et Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getNumeroVoyage() {
        return numeroVoyage;
    }
    
    public void setNumeroVoyage(String numeroVoyage) {
        this.numeroVoyage = numeroVoyage;
        this.dateModification = LocalDateTime.now();
    }
    
    public int getGareDepart() {
        return gareDepart;
    }
    
    public void setGareDepart(int gareDepart) {
        this.gareDepart = gareDepart;
        this.dateModification = LocalDateTime.now();
    }
    
    public int getGareArrivee() {
        return gareArrivee;
    }
    
    public void setGareArrivee(int gareArrivee) {
        this.gareArrivee = gareArrivee;
        this.dateModification = LocalDateTime.now();
    }
    
    public LocalDateTime getHeureDepart() {
        return heureDepart;
    }
    
    public void setHeureDepart(LocalDateTime heureDepart) {
        this.heureDepart = heureDepart;
        this.dateModification = LocalDateTime.now();
    }
    
    public LocalDateTime getHeureArrivee() {
        return heureArrivee;
    }
    
    public void setHeureArrivee(LocalDateTime heureArrivee) {
        this.heureArrivee = heureArrivee;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getTypeTrain() {
        return typeTrain;
    }
    
    public void setTypeTrain(String typeTrain) {
        this.typeTrain = typeTrain;
        this.dateModification = LocalDateTime.now();
    }
    
    public int getPlacesTotal() {
        return placesTotal;
    }
    
    public void setPlacesTotal(int placesTotal) {
        this.placesTotal = placesTotal;
        this.dateModification = LocalDateTime.now();
    }
    
    public int getPlacesDisponibles() {
        return placesDisponibles;
    }
    
    public void setPlacesDisponibles(int placesDisponibles) {
        this.placesDisponibles = placesDisponibles;
        this.dateModification = LocalDateTime.now();
    }
    
    public double getPrixBase() {
        return prixBase;
    }
    
    public void setPrixBase(double prixBase) {
        this.prixBase = prixBase;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getStatut() {
        return statut;
    }
    
    public void setStatut(String statut) {
        this.statut = statut;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getMotifAnnulation() {
        return motifAnnulation;
    }
    
    public void setMotifAnnulation(String motifAnnulation) {
        this.motifAnnulation = motifAnnulation;
        this.dateModification = LocalDateTime.now();
    }
    
    public int getRetardMinutes() {
        return retardMinutes;
    }
    
    public void setRetardMinutes(int retardMinutes) {
        this.retardMinutes = retardMinutes;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getVoie() {
        return voie;
    }
    
    public void setVoie(String voie) {
        this.voie = voie;
        this.dateModification = LocalDateTime.now();
    }
    
    public boolean isReservationOuverte() {
        return reservationOuverte;
    }
    
    public void setReservationOuverte(boolean reservationOuverte) {
        this.reservationOuverte = reservationOuverte;
        this.dateModification = LocalDateTime.now();
    }
    
    public LocalDateTime getDateCreation() {
        return dateCreation;
    }
    
    public void setDateCreation(LocalDateTime dateCreation) {
        this.dateCreation = dateCreation;
    }
    
    public LocalDateTime getDateModification() {
        return dateModification;
    }
    
    public void setDateModification(LocalDateTime dateModification) {
        this.dateModification = dateModification;
    }
    
    // Getters et Setters pour les informations des gares
    public String getNomGareDepart() {
        return nomGareDepart;
    }
    
    public void setNomGareDepart(String nomGareDepart) {
        this.nomGareDepart = nomGareDepart;
    }
    
    public String getVilleGareDepart() {
        return villeGareDepart;
    }
    
    public void setVilleGareDepart(String villeGareDepart) {
        this.villeGareDepart = villeGareDepart;
    }
    
    public String getNomGareArrivee() {
        return nomGareArrivee;
    }
    
    public void setNomGareArrivee(String nomGareArrivee) {
        this.nomGareArrivee = nomGareArrivee;
    }
    
    public String getVilleGareArrivee() {
        return villeGareArrivee;
    }
    
    public void setVilleGareArrivee(String villeGareArrivee) {
        this.villeGareArrivee = villeGareArrivee;
    }
    
    // Méthodes utilitaires
    public Duration getDureeVoyage() {
        if (heureDepart != null && heureArrivee != null) {
            return Duration.between(heureDepart, heureArrivee);
        }
        return Duration.ZERO;
    }
    
    public String getDureeFormatee() {
        Duration duree = getDureeVoyage();
        long heures = duree.toHours();
        long minutes = duree.toMinutesPart();
        return String.format("%dh%02d", heures, minutes);
    }
    
    public double getTauxOccupation() {
        if (placesTotal == 0) return 0.0;
        return ((double) (placesTotal - placesDisponibles) / placesTotal) * 100;
    }
    
    public boolean isComplet() {
        return placesDisponibles <= 0;
    }
    
    public boolean isAnnule() {
        return "ANNULE".equals(statut);
    }
    
    public boolean isEnRetard() {
        return retardMinutes > 0;
    }
    
    public LocalDateTime getHeureDepartRelle() {
        if (heureDepart != null && retardMinutes > 0) {
            return heureDepart.plusMinutes(retardMinutes);
        }
        return heureDepart;
    }
    
    public LocalDateTime getHeureArriveeRelle() {
        if (heureArrivee != null && retardMinutes > 0) {
            return heureArrivee.plusMinutes(retardMinutes);
        }
        return heureArrivee;
    }
    
    public String getTrajetComplet() {
        StringBuilder trajet = new StringBuilder();
        if (nomGareDepart != null && villeGareDepart != null) {
            trajet.append(nomGareDepart).append(" (").append(villeGareDepart).append(")");
        } else if (villeGareDepart != null) {
            trajet.append(villeGareDepart);
        }
        
        trajet.append(" → ");
        
        if (nomGareArrivee != null && villeGareArrivee != null) {
            trajet.append(nomGareArrivee).append(" (").append(villeGareArrivee).append(")");
        } else if (villeGareArrivee != null) {
            trajet.append(villeGareArrivee);
        }
        
        return trajet.toString();
    }
    
    @Override
    public String toString() {
        return "Voyage{" +
                "id=" + id +
                ", numeroVoyage='" + numeroVoyage + '\'' +
                ", trajet='" + getTrajetComplet() + '\'' +
                ", heureDepart=" + heureDepart +
                ", heureArrivee=" + heureArrivee +
                ", statut='" + statut + '\'' +
                ", placesDisponibles=" + placesDisponibles +
                '}';
    }
}
