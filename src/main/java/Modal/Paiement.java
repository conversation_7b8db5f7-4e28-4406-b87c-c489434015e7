package Modal;

import java.time.LocalDateTime;

public class Paiement {
    private int id;
    private int reservationId;
    private int userId;
    private double montant;
    private String methodePaiement; // CARTE_CREDIT, PAYPAL, VIREMENT, ESPECES
    private String statut; // EN_ATTENTE, VALIDE, REFUSE, REMBOURSE, ANNULE
    private String numeroTransaction;
    private String referenceExterne;
    private LocalDateTime datePaiement;
    private LocalDateTime dateValidation;
    private LocalDateTime dateRemboursement;
    private String motifRefus;
    private String motifRemboursement;
    private double montantRembourse;
    private double fraisTransaction;
    private String deviseOrigine;
    private double tauxChange;
    private String adresseFacturation;
    private String nomTitulaire;
    private String dernierQuatreChiffres; // Pour les cartes
    private LocalDateTime dateCreation;
    private LocalDateTime dateModification;
    
    // Informations liées (pour l'affichage)
    private String usernameClient;
    private String emailClient;
    private String numeroTicket;
    private String trajetReservation;
    
    // Constructeurs
    public Paiement() {
        this.dateCreation = LocalDateTime.now();
        this.dateModification = LocalDateTime.now();
        this.statut = "EN_ATTENTE";
        this.deviseOrigine = "EUR";
        this.tauxChange = 1.0;
        this.fraisTransaction = 0.0;
    }
    
    public Paiement(int reservationId, int userId, double montant, String methodePaiement) {
        this();
        this.reservationId = reservationId;
        this.userId = userId;
        this.montant = montant;
        this.methodePaiement = methodePaiement;
        this.datePaiement = LocalDateTime.now();
    }
    
    // Getters et Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public int getReservationId() {
        return reservationId;
    }
    
    public void setReservationId(int reservationId) {
        this.reservationId = reservationId;
        this.dateModification = LocalDateTime.now();
    }
    
    public int getUserId() {
        return userId;
    }
    
    public void setUserId(int userId) {
        this.userId = userId;
        this.dateModification = LocalDateTime.now();
    }
    
    public double getMontant() {
        return montant;
    }
    
    public void setMontant(double montant) {
        this.montant = montant;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getMethodePaiement() {
        return methodePaiement;
    }
    
    public void setMethodePaiement(String methodePaiement) {
        this.methodePaiement = methodePaiement;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getStatut() {
        return statut;
    }
    
    public void setStatut(String statut) {
        this.statut = statut;
        this.dateModification = LocalDateTime.now();
        
        // Mettre à jour les dates selon le statut
        if ("VALIDE".equals(statut) && dateValidation == null) {
            this.dateValidation = LocalDateTime.now();
        } else if ("REMBOURSE".equals(statut) && dateRemboursement == null) {
            this.dateRemboursement = LocalDateTime.now();
        }
    }
    
    public String getNumeroTransaction() {
        return numeroTransaction;
    }
    
    public void setNumeroTransaction(String numeroTransaction) {
        this.numeroTransaction = numeroTransaction;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getReferenceExterne() {
        return referenceExterne;
    }
    
    public void setReferenceExterne(String referenceExterne) {
        this.referenceExterne = referenceExterne;
        this.dateModification = LocalDateTime.now();
    }
    
    public LocalDateTime getDatePaiement() {
        return datePaiement;
    }
    
    public void setDatePaiement(LocalDateTime datePaiement) {
        this.datePaiement = datePaiement;
        this.dateModification = LocalDateTime.now();
    }
    
    public LocalDateTime getDateValidation() {
        return dateValidation;
    }
    
    public void setDateValidation(LocalDateTime dateValidation) {
        this.dateValidation = dateValidation;
        this.dateModification = LocalDateTime.now();
    }
    
    public LocalDateTime getDateRemboursement() {
        return dateRemboursement;
    }
    
    public void setDateRemboursement(LocalDateTime dateRemboursement) {
        this.dateRemboursement = dateRemboursement;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getMotifRefus() {
        return motifRefus;
    }
    
    public void setMotifRefus(String motifRefus) {
        this.motifRefus = motifRefus;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getMotifRemboursement() {
        return motifRemboursement;
    }
    
    public void setMotifRemboursement(String motifRemboursement) {
        this.motifRemboursement = motifRemboursement;
        this.dateModification = LocalDateTime.now();
    }
    
    public double getMontantRembourse() {
        return montantRembourse;
    }
    
    public void setMontantRembourse(double montantRembourse) {
        this.montantRembourse = montantRembourse;
        this.dateModification = LocalDateTime.now();
    }
    
    public double getFraisTransaction() {
        return fraisTransaction;
    }
    
    public void setFraisTransaction(double fraisTransaction) {
        this.fraisTransaction = fraisTransaction;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getDeviseOrigine() {
        return deviseOrigine;
    }
    
    public void setDeviseOrigine(String deviseOrigine) {
        this.deviseOrigine = deviseOrigine;
        this.dateModification = LocalDateTime.now();
    }
    
    public double getTauxChange() {
        return tauxChange;
    }
    
    public void setTauxChange(double tauxChange) {
        this.tauxChange = tauxChange;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getAdresseFacturation() {
        return adresseFacturation;
    }
    
    public void setAdresseFacturation(String adresseFacturation) {
        this.adresseFacturation = adresseFacturation;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getNomTitulaire() {
        return nomTitulaire;
    }
    
    public void setNomTitulaire(String nomTitulaire) {
        this.nomTitulaire = nomTitulaire;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getDernierQuatreChiffres() {
        return dernierQuatreChiffres;
    }
    
    public void setDernierQuatreChiffres(String dernierQuatreChiffres) {
        this.dernierQuatreChiffres = dernierQuatreChiffres;
        this.dateModification = LocalDateTime.now();
    }
    
    public LocalDateTime getDateCreation() {
        return dateCreation;
    }
    
    public void setDateCreation(LocalDateTime dateCreation) {
        this.dateCreation = dateCreation;
    }
    
    public LocalDateTime getDateModification() {
        return dateModification;
    }
    
    public void setDateModification(LocalDateTime dateModification) {
        this.dateModification = dateModification;
    }
    
    // Getters et Setters pour les informations liées
    public String getUsernameClient() {
        return usernameClient;
    }
    
    public void setUsernameClient(String usernameClient) {
        this.usernameClient = usernameClient;
    }
    
    public String getEmailClient() {
        return emailClient;
    }
    
    public void setEmailClient(String emailClient) {
        this.emailClient = emailClient;
    }
    
    public String getNumeroTicket() {
        return numeroTicket;
    }
    
    public void setNumeroTicket(String numeroTicket) {
        this.numeroTicket = numeroTicket;
    }
    
    public String getTrajetReservation() {
        return trajetReservation;
    }
    
    public void setTrajetReservation(String trajetReservation) {
        this.trajetReservation = trajetReservation;
    }
    
    // Méthodes utilitaires
    public boolean isValide() {
        return "VALIDE".equals(statut);
    }
    
    public boolean isRembourse() {
        return "REMBOURSE".equals(statut);
    }
    
    public boolean isEnAttente() {
        return "EN_ATTENTE".equals(statut);
    }
    
    public boolean isRefuse() {
        return "REFUSE".equals(statut);
    }
    
    public double getMontantNet() {
        return montant - fraisTransaction;
    }
    
    public String getMethodePaiementFormatee() {
        switch (methodePaiement) {
            case "CARTE_CREDIT": return "Carte de crédit";
            case "PAYPAL": return "PayPal";
            case "VIREMENT": return "Virement bancaire";
            case "ESPECES": return "Espèces";
            default: return methodePaiement;
        }
    }
    
    public String getStatutFormate() {
        switch (statut) {
            case "EN_ATTENTE": return "En attente";
            case "VALIDE": return "Validé";
            case "REFUSE": return "Refusé";
            case "REMBOURSE": return "Remboursé";
            case "ANNULE": return "Annulé";
            default: return statut;
        }
    }
    
    public String getCarteFormatee() {
        if (dernierQuatreChiffres != null && dernierQuatreChiffres.length() >= 4) {
            return "**** **** **** " + dernierQuatreChiffres;
        }
        return dernierQuatreChiffres;
    }
    
    @Override
    public String toString() {
        return "Paiement{" +
                "id=" + id +
                ", reservationId=" + reservationId +
                ", montant=" + montant +
                ", methodePaiement='" + methodePaiement + '\'' +
                ", statut='" + statut + '\'' +
                ", numeroTransaction='" + numeroTransaction + '\'' +
                ", datePaiement=" + datePaiement +
                '}';
    }
}
