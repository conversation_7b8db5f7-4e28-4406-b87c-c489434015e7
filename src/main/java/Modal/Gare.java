package Modal;

import java.time.LocalDateTime;

public class Gare {
    private int id;
    private String nom;
    private String ville;
    private String adresse;
    private String codePostal;
    private String region;
    private String pays;
    private double latitude;
    private double longitude;
    private String telephone;
    private String email;
    private boolean active;
    private int capaciteMax;
    private String services; // JSON ou texte des services disponibles
    private LocalDateTime dateCreation;
    private LocalDateTime dateModification;
    
    // Constructeurs
    public Gare() {
        this.dateCreation = LocalDateTime.now();
        this.dateModification = LocalDateTime.now();
        this.active = true;
        this.pays = "France";
    }
    
    public Gare(String nom, String ville, String adresse) {
        this();
        this.nom = nom;
        this.ville = ville;
        this.adresse = adresse;
    }
    
    // Getters et Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getNom() {
        return nom;
    }
    
    public void setNom(String nom) {
        this.nom = nom;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getVille() {
        return ville;
    }
    
    public void setVille(String ville) {
        this.ville = ville;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getAdresse() {
        return adresse;
    }
    
    public void setAdresse(String adresse) {
        this.adresse = adresse;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getCodePostal() {
        return codePostal;
    }
    
    public void setCodePostal(String codePostal) {
        this.codePostal = codePostal;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getRegion() {
        return region;
    }
    
    public void setRegion(String region) {
        this.region = region;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getPays() {
        return pays;
    }
    
    public void setPays(String pays) {
        this.pays = pays;
        this.dateModification = LocalDateTime.now();
    }
    
    public double getLatitude() {
        return latitude;
    }
    
    public void setLatitude(double latitude) {
        this.latitude = latitude;
        this.dateModification = LocalDateTime.now();
    }
    
    public double getLongitude() {
        return longitude;
    }
    
    public void setLongitude(double longitude) {
        this.longitude = longitude;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getTelephone() {
        return telephone;
    }
    
    public void setTelephone(String telephone) {
        this.telephone = telephone;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
        this.dateModification = LocalDateTime.now();
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
        this.dateModification = LocalDateTime.now();
    }
    
    public int getCapaciteMax() {
        return capaciteMax;
    }
    
    public void setCapaciteMax(int capaciteMax) {
        this.capaciteMax = capaciteMax;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getServices() {
        return services;
    }
    
    public void setServices(String services) {
        this.services = services;
        this.dateModification = LocalDateTime.now();
    }
    
    public LocalDateTime getDateCreation() {
        return dateCreation;
    }
    
    public void setDateCreation(LocalDateTime dateCreation) {
        this.dateCreation = dateCreation;
    }
    
    public LocalDateTime getDateModification() {
        return dateModification;
    }
    
    public void setDateModification(LocalDateTime dateModification) {
        this.dateModification = dateModification;
    }
    
    // Méthodes utilitaires
    public String getNomComplet() {
        return nom + " (" + ville + ")";
    }
    
    public String getAdresseComplete() {
        StringBuilder adresseComplete = new StringBuilder();
        if (adresse != null) adresseComplete.append(adresse);
        if (codePostal != null) adresseComplete.append(", ").append(codePostal);
        if (ville != null) adresseComplete.append(" ").append(ville);
        if (region != null) adresseComplete.append(", ").append(region);
        if (pays != null) adresseComplete.append(", ").append(pays);
        return adresseComplete.toString();
    }
    
    public boolean hasCoordinates() {
        return latitude != 0.0 && longitude != 0.0;
    }
    
    @Override
    public String toString() {
        return "Gare{" +
                "id=" + id +
                ", nom='" + nom + '\'' +
                ", ville='" + ville + '\'' +
                ", adresse='" + adresse + '\'' +
                ", active=" + active +
                ", capaciteMax=" + capaciteMax +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Gare gare = (Gare) obj;
        return id == gare.id;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(id);
    }
}
