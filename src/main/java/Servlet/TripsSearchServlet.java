package Servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.google.gson.Gson;
import com.google.gson.JsonObject;

import Modal.Trip;
import utils.DatabaseConnection;

@WebServlet("/api/trips-search")
public class TripsSearchServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private Gson gson = new Gson();

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        // Permettre les requêtes CORS pour l'API publique
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type");
        
        PrintWriter out = response.getWriter();
        JsonObject jsonResponse = new JsonObject();
        
        try {
            String action = request.getParameter("action");
            
            if ("search".equals(action)) {
                handleSearchTrips(request, jsonResponse);
            } else {
                jsonResponse.addProperty("success", false);
                jsonResponse.addProperty("message", "Action non supportée");
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            jsonResponse.addProperty("success", false);
            jsonResponse.addProperty("message", "Erreur interne du serveur: " + e.getMessage());
        }
        
        out.print(gson.toJson(jsonResponse));
        out.flush();
    }
    
    private void handleSearchTrips(HttpServletRequest request, JsonObject jsonResponse) {
        String departureCity = request.getParameter("departureCity");
        String destinationCity = request.getParameter("destinationCity");
        String travelDate = request.getParameter("travelDate");
        
        // Validation des paramètres
        if (departureCity == null || departureCity.trim().isEmpty()) {
            jsonResponse.addProperty("success", false);
            jsonResponse.addProperty("message", "La ville de départ est requise");
            return;
        }
        
        if (destinationCity == null || destinationCity.trim().isEmpty()) {
            jsonResponse.addProperty("success", false);
            jsonResponse.addProperty("message", "La ville de destination est requise");
            return;
        }
        
        if (travelDate == null || travelDate.trim().isEmpty()) {
            jsonResponse.addProperty("success", false);
            jsonResponse.addProperty("message", "La date de voyage est requise");
            return;
        }
        
        // Validation de la date
        LocalDate searchDate;
        try {
            searchDate = LocalDate.parse(travelDate, DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate today = LocalDate.now();
            
            if (searchDate.isBefore(today)) {
                jsonResponse.addProperty("success", false);
                jsonResponse.addProperty("message", "La date de voyage ne peut pas être dans le passé");
                return;
            }
        } catch (DateTimeParseException e) {
            jsonResponse.addProperty("success", false);
            jsonResponse.addProperty("message", "Format de date invalide");
            return;
        }
        
        // Validation des villes (ne peuvent pas être identiques)
        if (departureCity.trim().equalsIgnoreCase(destinationCity.trim())) {
            jsonResponse.addProperty("success", false);
            jsonResponse.addProperty("message", "La ville de départ et la destination doivent être différentes");
            return;
        }
        
        try {
            List<Trip> trips = searchTrips(departureCity.trim(), destinationCity.trim(), travelDate);
            
            jsonResponse.addProperty("success", true);
            jsonResponse.addProperty("message", "Recherche effectuée avec succès");
            jsonResponse.addProperty("total", trips.size());
            jsonResponse.add("trips", gson.toJsonTree(trips));
            
        } catch (SQLException e) {
            e.printStackTrace();
            jsonResponse.addProperty("success", false);
            jsonResponse.addProperty("message", "Erreur lors de la recherche dans la base de données");
        }
    }
    
    private List<Trip> searchTrips(String departureCity, String destinationCity, String travelDate) 
            throws SQLException {
        
        List<Trip> trips = new ArrayList<>();
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DatabaseConnection.getConnection();
            
            // Requête pour rechercher les trips selon les critères
            // On recherche les trips qui partent le jour demandé
            String sql = "SELECT * FROM trips WHERE " +
                        "LOWER(departure_city) = LOWER(?) AND " +
                        "LOWER(destination_city) = LOWER(?) AND " +
                        "DATE(departure_time) = ? " +
                        "ORDER BY departure_time ASC";
            
            stmt = conn.prepareStatement(sql);
            stmt.setString(1, departureCity);
            stmt.setString(2, destinationCity);
            stmt.setString(3, travelDate);
            
            rs = stmt.executeQuery();
            
            while (rs.next()) {
                Trip trip = new Trip();
                trip.setId(rs.getInt("id"));
                trip.setDepartureCity(rs.getString("departure_city"));
                trip.setDestinationCity(rs.getString("destination_city"));
                trip.setDepartureTime(rs.getTimestamp("departure_time").toLocalDateTime());
                trip.setArrivalTime(rs.getTimestamp("arrival_time").toLocalDateTime());
                trip.setPrice(rs.getDouble("price"));
                trip.setAvailableSeats(rs.getInt("available_seats"));
                
                trips.add(trip);
            }
            
        } finally {
            if (rs != null) try { rs.close(); } catch (SQLException e) { e.printStackTrace(); }
            if (stmt != null) try { stmt.close(); } catch (SQLException e) { e.printStackTrace(); }
            if (conn != null) try { conn.close(); } catch (SQLException e) { e.printStackTrace(); }
        }
        
        return trips;
    }
    
    @Override
    protected void doOptions(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // Gérer les requêtes OPTIONS pour CORS
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type");
        response.setStatus(200);
    }
}
