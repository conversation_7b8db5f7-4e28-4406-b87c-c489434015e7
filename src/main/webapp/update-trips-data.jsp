<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.sql.*" %>
<%@ page import="utils.DatabaseConnection" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mise à jour des données de voyages - TrainApp</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-6">
                    <i class="fas fa-database text-blue-600 mr-3"></i>
                    Mise à jour des données de voyages
                </h1>
                
                <%
                String action = request.getParameter("action");
                if ("update".equals(action)) {
                    try {
                        Connection conn = DatabaseConnection.getConnection();
                        
                        // Supprimer les anciens voyages
                        out.println("<div class='mb-4 p-4 bg-yellow-100 border border-yellow-400 rounded'>");
                        out.println("<p class='text-yellow-800'><i class='fas fa-trash mr-2'></i>Suppression des anciens voyages...</p>");
                        
                        Statement stmt = conn.createStatement();
                        int deletedRows = stmt.executeUpdate("DELETE FROM trips");
                        out.println("<p class='text-yellow-800'>✓ " + deletedRows + " voyage(s) supprimé(s)</p>");
                        out.println("</div>");
                        
                        // Insérer les nouveaux voyages avec des dates actuelles
                        out.println("<div class='mb-4 p-4 bg-blue-100 border border-blue-400 rounded'>");
                        out.println("<p class='text-blue-800'><i class='fas fa-plus mr-2'></i>Insertion des nouveaux voyages...</p>");
                        
                        String insertSQL = "INSERT INTO trips (departure_city, destination_city, departure_time, arrival_time, price, available_seats) VALUES (?, ?, ?, ?, ?, ?)";
                        PreparedStatement pstmt = conn.prepareStatement(insertSQL);
                        
                        // Voyages d'aujourd'hui
                        String[][] trips = {
                            {"Paris", "Lyon", "DATE_ADD(CURDATE(), INTERVAL 8 HOUR)", "DATE_ADD(CURDATE(), INTERVAL 10 HOUR + 30 MINUTE)", "45.50", "120"},
                            {"Lyon", "Marseille", "DATE_ADD(CURDATE(), INTERVAL 14 HOUR)", "DATE_ADD(CURDATE(), INTERVAL 16 HOUR + 45 MINUTE)", "35.00", "80"},
                            {"Paris", "Marseille", "DATE_ADD(CURDATE(), INTERVAL 9 HOUR + 15 MINUTE)", "DATE_ADD(CURDATE(), INTERVAL 12 HOUR + 30 MINUTE)", "75.00", "150"},
                            {"Marseille", "Nice", "DATE_ADD(CURDATE(), INTERVAL 16 HOUR)", "DATE_ADD(CURDATE(), INTERVAL 18 HOUR + 30 MINUTE)", "25.00", "60"},
                            {"Nice", "Monaco", "DATE_ADD(CURDATE(), INTERVAL 19 HOUR)", "DATE_ADD(CURDATE(), INTERVAL 19 HOUR + 45 MINUTE)", "15.00", "40"},
                            
                            // Voyages de demain
                            {"Paris", "Lyon", "DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 7 HOUR", "DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 9 HOUR + 30 MINUTE", "45.50", "100"},
                            {"Lyon", "Paris", "DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 15 HOUR", "DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 17 HOUR + 30 MINUTE", "45.50", "90"},
                            {"Paris", "Bordeaux", "DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 10 HOUR", "DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 13 HOUR + 15 MINUTE", "65.00", "110"},
                            {"Bordeaux", "Toulouse", "DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 16 HOUR", "DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 18 HOUR + 30 MINUTE", "30.00", "70"},
                            {"Paris", "Strasbourg", "DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 11 HOUR", "DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 14 HOUR + 45 MINUTE", "55.00", "85"},
                            
                            // Voyages d'après-demain
                            {"Lyon", "Marseille", "DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 8 HOUR", "DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 10 HOUR + 45 MINUTE", "35.00", "95"},
                            {"Marseille", "Lyon", "DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 17 HOUR", "DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 19 HOUR + 45 MINUTE", "35.00", "75"},
                            {"Paris", "Lille", "DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 9 HOUR", "DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 10 HOUR + 15 MINUTE", "40.00", "130"},
                            {"Lille", "Paris", "DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 18 HOUR", "DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 19 HOUR + 15 MINUTE", "40.00", "120"},
                            {"Toulouse", "Montpellier", "DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 12 HOUR", "DATE_ADD(CURDATE(), INTERVAL 2 DAY) + INTERVAL 14 HOUR + 30 MINUTE", "28.00", "65"}
                        };
                        
                        int insertedCount = 0;
                        for (String[] trip : trips) {
                            String insertWithDates = "INSERT INTO trips (departure_city, destination_city, departure_time, arrival_time, price, available_seats) VALUES ('" +
                                trip[0] + "', '" + trip[1] + "', " + trip[2] + ", " + trip[3] + ", " + trip[4] + ", " + trip[5] + ")";
                            
                            int result = stmt.executeUpdate(insertWithDates);
                            if (result > 0) {
                                insertedCount++;
                                out.println("<p class='text-blue-800'>✓ " + trip[0] + " → " + trip[1] + " (" + trip[4] + "€)</p>");
                            }
                        }
                        
                        out.println("<p class='text-blue-800 font-bold mt-2'>Total: " + insertedCount + " voyage(s) ajouté(s)</p>");
                        out.println("</div>");
                        
                        // Afficher un résumé
                        out.println("<div class='mb-4 p-4 bg-green-100 border border-green-400 rounded'>");
                        out.println("<p class='text-green-800'><i class='fas fa-check-circle mr-2'></i><strong>Mise à jour terminée avec succès !</strong></p>");
                        
                        ResultSet rs = stmt.executeQuery("SELECT COUNT(*) as total FROM trips");
                        if (rs.next()) {
                            out.println("<p class='text-green-800'>Total des voyages en base: " + rs.getInt("total") + "</p>");
                        }
                        
                        // Afficher quelques exemples
                        out.println("<h3 class='text-green-800 font-bold mt-4 mb-2'>Exemples de voyages disponibles:</h3>");
                        rs = stmt.executeQuery("SELECT departure_city, destination_city, DATE_FORMAT(departure_time, '%Y-%m-%d %H:%i') as departure, price FROM trips ORDER BY departure_time LIMIT 5");
                        while (rs.next()) {
                            out.println("<p class='text-green-700'>• " + rs.getString("departure_city") + " → " + rs.getString("destination_city") + 
                                       " le " + rs.getString("departure") + " (" + rs.getDouble("price") + "€)</p>");
                        }
                        out.println("</div>");
                        
                        conn.close();
                        
                    } catch (Exception e) {
                        out.println("<div class='mb-4 p-4 bg-red-100 border border-red-400 rounded'>");
                        out.println("<p class='text-red-800'><i class='fas fa-exclamation-triangle mr-2'></i><strong>Erreur:</strong> " + e.getMessage() + "</p>");
                        out.println("</div>");
                        e.printStackTrace();
                    }
                } else {
                %>
                
                <div class="mb-6 p-4 bg-blue-100 border border-blue-400 rounded">
                    <p class="text-blue-800">
                        <i class="fas fa-info-circle mr-2"></i>
                        Cette page permet de mettre à jour les données de voyages avec des dates actuelles et futures.
                    </p>
                </div>
                
                <div class="mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Actions disponibles :</h2>
                    <ul class="list-disc list-inside text-gray-700 space-y-2">
                        <li>Suppression de tous les anciens voyages</li>
                        <li>Insertion de nouveaux voyages avec des dates actuelles</li>
                        <li>Voyages pour aujourd'hui, demain et les jours suivants</li>
                        <li>Différentes villes : Paris, Lyon, Marseille, Nice, Bordeaux, etc.</li>
                    </ul>
                </div>
                
                <div class="text-center">
                    <a href="?action=update" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Mettre à jour les données
                    </a>
                </div>
                
                <% } %>
                
                <div class="mt-8 text-center">
                    <a href="views/search-trains.jsp" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200">
                        <i class="fas fa-search mr-2"></i>
                        Tester la recherche de trains
                    </a>
                    <a href="views/index.jsp" class="ml-4 bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200">
                        <i class="fas fa-home mr-2"></i>
                        Retour à l'accueil
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
