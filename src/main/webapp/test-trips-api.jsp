<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Trips - TrainApp</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-6">
                    <i class="fas fa-flask text-blue-600 mr-3"></i>
                    Test API Trips - Récupération depuis la base de données
                </h1>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h3 class="font-bold text-blue-800 mb-2">
                            <i class="fas fa-database mr-2"></i>Test API All Trips
                        </h3>
                        <button onclick="testAllTripsAPI()" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-play mr-2"></i>Tester /api/all-trips
                        </button>
                    </div>
                    
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h3 class="font-bold text-green-800 mb-2">
                            <i class="fas fa-calendar mr-2"></i>Test Filtrage par Date
                        </h3>
                        <button onclick="testTodayTrips()" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mr-2">
                            <i class="fas fa-calendar-day mr-2"></i>Aujourd'hui
                        </button>
                        <button onclick="testTomorrowTrips()" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                            <i class="fas fa-calendar-plus mr-2"></i>Demain
                        </button>
                    </div>
                </div>
                
                <!-- Résultats -->
                <div id="results" class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Résultats des tests :</h3>
                    <div id="resultsContent" class="text-gray-600">
                        Cliquez sur un bouton pour tester l'API...
                    </div>
                </div>
                
                <!-- Données brutes -->
                <div id="rawData" class="mt-6 bg-gray-900 text-green-400 p-4 rounded-lg" style="display: none;">
                    <h3 class="text-lg font-semibold mb-4">Données JSON brutes :</h3>
                    <pre id="rawDataContent" class="text-sm overflow-x-auto"></pre>
                </div>
                
                <div class="mt-8 text-center">
                    <a href="views/all-trips.jsp" class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded mr-4">
                        <i class="fas fa-list mr-2"></i>Aller à la page Tous les voyages
                    </a>
                    <a href="update-trips-data.jsp" class="bg-orange-600 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-database mr-2"></i>Mettre à jour les données
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showResults(title, data, success = true) {
            const resultsContent = document.getElementById('resultsContent');
            const rawData = document.getElementById('rawData');
            const rawDataContent = document.getElementById('rawDataContent');
            
            const statusIcon = success ? '<i class="fas fa-check-circle text-green-600 mr-2"></i>' : '<i class="fas fa-times-circle text-red-600 mr-2"></i>';
            const statusClass = success ? 'text-green-800' : 'text-red-800';
            
            let html = '<div class="' + statusClass + ' font-semibold mb-4">' + statusIcon + title + '</div>';
            
            if (data && data.success) {
                html += '<div class="mb-4">';
                html += '<p class="text-gray-700 mb-2"><strong>Message:</strong> ' + data.message + '</p>';
                html += '<p class="text-gray-700 mb-2"><strong>Total voyages:</strong> ' + data.total + '</p>';
                
                if (data.data && data.data.length > 0) {
                    html += '<div class="mt-4">';
                    html += '<h4 class="font-semibold text-gray-800 mb-2">Voyages trouvés:</h4>';
                    html += '<div class="grid grid-cols-1 gap-2">';
                    
                    data.data.forEach(trip => {
                        html += '<div class="bg-white p-3 rounded border">';
                        html += '<div class="flex justify-between items-center">';
                        html += '<div>';
                        html += '<span class="font-semibold">' + trip.departureCity + ' → ' + trip.destinationCity + '</span>';
                        html += '<div class="text-sm text-gray-600">';
                        html += 'Départ: ' + trip.departureTime + ' | ';
                        html += 'Prix: ' + trip.price + '€ | ';
                        html += 'Places: ' + trip.availableSeats;
                        html += '</div>';
                        html += '</div>';
                        html += '</div>';
                        html += '</div>';
                    });
                    
                    html += '</div>';
                    html += '</div>';
                }
                html += '</div>';
            } else if (data && !data.success) {
                html += '<div class="text-red-700 mb-4">';
                html += '<p><strong>Erreur:</strong> ' + (data.message || 'Erreur inconnue') + '</p>';
                html += '</div>';
            }
            
            resultsContent.innerHTML = html;
            
            // Afficher les données brutes
            rawDataContent.textContent = JSON.stringify(data, null, 2);
            rawData.style.display = 'block';
        }
        
        function testAllTripsAPI() {
            const resultsContent = document.getElementById('resultsContent');
            resultsContent.innerHTML = '<div class="text-blue-600"><i class="fas fa-spinner fa-spin mr-2"></i>Test en cours...</div>';
            
            fetch('api/all-trips?action=getAll')
                .then(response => response.json())
                .then(data => {
                    showResults('Test API All Trips - Succès', data, true);
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showResults('Test API All Trips - Erreur', { success: false, message: error.message }, false);
                });
        }
        
        function testTodayTrips() {
            const resultsContent = document.getElementById('resultsContent');
            resultsContent.innerHTML = '<div class="text-green-600"><i class="fas fa-spinner fa-spin mr-2"></i>Test en cours...</div>';
            
            fetch('api/all-trips?action=getByDate&date=today')
                .then(response => response.json())
                .then(data => {
                    showResults('Test Voyages Aujourd\'hui - Succès', data, true);
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showResults('Test Voyages Aujourd\'hui - Erreur', { success: false, message: error.message }, false);
                });
        }
        
        function testTomorrowTrips() {
            const resultsContent = document.getElementById('resultsContent');
            resultsContent.innerHTML = '<div class="text-green-600"><i class="fas fa-spinner fa-spin mr-2"></i>Test en cours...</div>';
            
            fetch('api/all-trips?action=getByDate&date=tomorrow')
                .then(response => response.json())
                .then(data => {
                    showResults('Test Voyages Demain - Succès', data, true);
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showResults('Test Voyages Demain - Erreur', { success: false, message: error.message }, false);
                });
        }
        
        // Test automatique au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testAllTripsAPI();
            }, 1000);
        });
    </script>
</body>
</html>
