<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="Modal.User" %>
<%
    User user = (session != null) ? (User) session.getAttribute("user") : null;
    if (user == null || !user.getRole().equals("admin")) {
        response.sendRedirect(request.getContextPath() + "/views/login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Admin - TrainTicket Pro</title>
    <!-- Inclure Tailwind CSS via CDN -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- Inclure Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* Styles personnalisés pour un look ultra-professionnel */
        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            min-height: 100vh;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
            width: 280px;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: #cbd5e1;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .menu-item:hover, .menu-item.active {
            background: rgba(59, 130, 246, 0.1);
            color: #60a5fa;
            border-left-color: #3b82f6;
        }

        .menu-item i {
            width: 20px;
            margin-right: 0.75rem;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            padding: 0;
        }

        /* Top Bar */
        .top-bar {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-bar {
            flex: 1;
            max-width: 400px;
            margin: 0 2rem;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem 1rem 0.5rem 2.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
        }

        /* Dashboard Content */
        .dashboard-content {
            padding: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #f1f5f9;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stat-icon {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 2rem;
            opacity: 0.3;
        }

        .chart-container {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }

        .recent-activity {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }

        .activity-header {
            padding: 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            background: #f8fafc;
        }

        .activity-item {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            align-items: center;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .notification-badge {
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            position: absolute;
            top: -8px;
            right: -8px;
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
<div class="main-container">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h2 class="text-white text-xl font-bold">
                <i class="fas fa-train mr-2 text-blue-400"></i>
                TrainTicket Pro
            </h2>
            <p class="text-slate-400 text-sm mt-1">Admin Dashboard</p>
        </div>

        <nav class="sidebar-menu">
            <a href="#dashboard" class="menu-item active" onclick="showSection('dashboard')">
                <i class="fas fa-tachometer-alt"></i>
                <span>Dashboard</span>
            </a>
            <a href="#users" class="menu-item" onclick="showSection('users')">
                <i class="fas fa-users"></i>
                <span>Gestion Utilisateurs</span>
            </a>
            <a href="#routes" class="menu-item" onclick="showSection('routes')">
                <i class="fas fa-route"></i>
                <span>Gestion Trajets</span>
            </a>
            <a href="#stations" class="menu-item" onclick="showSection('stations')">
                <i class="fas fa-map-marker-alt"></i>
                <span>Gestion Gares</span>
            </a>
            <a href="#trips" class="menu-item" onclick="showSection('trips')">
                <i class="fas fa-train"></i>
                <span>Gestion Voyages</span>
            </a>
            <a href="#seat-availability" class="menu-item" onclick="showSection('seat-availability')">
                <i class="fas fa-chair"></i>
                <span>Disponibilité Places</span>
            </a>
            <a href="#payments" class="menu-item" onclick="showSection('payments')">
                <i class="fas fa-credit-card"></i>
                <span>Gestion Paiements</span>
            </a>
            <a href="#cancellations" class="menu-item" onclick="showSection('cancellations')">
                <i class="fas fa-times-circle"></i>
                <span>Annulations & Remboursements</span>
            </a>
            <a href="#analytics" class="menu-item" onclick="showSection('analytics')">
                <i class="fas fa-chart-line"></i>
                <span>Analytiques</span>
            </a>
            <a href="#reports" class="menu-item" onclick="showSection('reports')">
                <i class="fas fa-file-alt"></i>
                <span>Rapports</span>
            </a>
            <a href="#settings" class="menu-item" onclick="showSection('settings')">
                <i class="fas fa-cog"></i>
                <span>Paramètres</span>
            </a>
            <a href="#logs" class="menu-item" onclick="showSection('logs')">
                <i class="fas fa-list-alt"></i>
                <span>Logs Système</span>
            </a>
        </nav>

        <div style="position: absolute; bottom: 1rem; left: 1.5rem; right: 1.5rem;">
            <a href="<%= request.getContextPath() %>/logout" class="menu-item text-red-400 hover:text-red-300">
                <i class="fas fa-sign-out-alt"></i>
                <span>Déconnexion</span>
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="flex items-center">
                <button class="md:hidden mr-4 text-gray-600">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="text-xl font-semibold text-gray-800">Dashboard Administrateur</h1>
            </div>

            <div class="search-bar relative">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Rechercher...">
            </div>

            <div class="flex items-center space-x-4">
                <div class="relative">
                    <button class="text-gray-600 hover:text-gray-800">
                        <i class="fas fa-bell text-xl"></i>
                        <span class="notification-badge">3</span>
                    </button>
                </div>
                <div class="flex items-center space-x-2">
                    <img src="https://ui-avatars.com/api/?name=<%= user.getUsername() %>&background=3b82f6&color=fff"
                         alt="Avatar" class="w-8 h-8 rounded-full">
                    <span class="text-gray-700 font-medium"><%= user.getUsername() %></span>
                </div>
            </div>
        </div>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Dashboard Section -->
            <div id="dashboard" class="section active">
                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon text-blue-500">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number" id="totalUsers">156</div>
                        <div class="stat-label">Utilisateurs Total</div>
                        <div class="text-sm text-green-600 mt-2">
                            <i class="fas fa-arrow-up"></i> +12% ce mois
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon text-green-500">
                            <i class="fas fa-train"></i>
                        </div>
                        <div class="stat-number" id="activeTrains">24</div>
                        <div class="stat-label">Trains Actifs</div>
                        <div class="text-sm text-green-600 mt-2">
                            <i class="fas fa-arrow-up"></i> +3 nouveaux
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon text-purple-500">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="stat-number" id="totalReservations">1,247</div>
                        <div class="stat-label">Réservations</div>
                        <div class="text-sm text-green-600 mt-2">
                            <i class="fas fa-arrow-up"></i> +8% cette semaine
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon text-yellow-500">
                            <i class="fas fa-euro-sign"></i>
                        </div>
                        <div class="stat-number" id="totalRevenue">€45,230</div>
                        <div class="stat-label">Revenus</div>
                        <div class="text-sm text-green-600 mt-2">
                            <i class="fas fa-arrow-up"></i> +15% ce mois
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-4">Réservations par Mois</h3>
                        <canvas id="reservationsChart" width="400" height="200"></canvas>
                    </div>

                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-4">Revenus par Ligne</h3>
                        <canvas id="revenueChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="recent-activity">
                    <div class="activity-header">
                        <h3 class="text-lg font-semibold">Activité Récente</h3>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon bg-blue-100 text-blue-600">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium">Nouvel utilisateur inscrit</p>
                            <p class="text-sm text-gray-500">Marie Dubois s'est inscrite</p>
                        </div>
                        <div class="text-sm text-gray-400">Il y a 5 min</div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon bg-green-100 text-green-600">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium">Nouvelle réservation</p>
                            <p class="text-sm text-gray-500">Paris → Lyon, 2 places</p>
                        </div>
                        <div class="text-sm text-gray-400">Il y a 12 min</div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon bg-purple-100 text-purple-600">
                            <i class="fas fa-train"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium">Train ajouté</p>
                            <p class="text-sm text-gray-500">TGV 8745 - Ligne Paris-Marseille</p>
                        </div>
                        <div class="text-sm text-gray-400">Il y a 1h</div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon bg-yellow-100 text-yellow-600">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium">Alerte système</p>
                            <p class="text-sm text-gray-500">Maintenance programmée demain</p>
                        </div>
                        <div class="text-sm text-gray-400">Il y a 2h</div>
                    </div>
                </div>
            </div>

            <!-- Users Management Section -->
            <div id="users" class="section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Gestion des Utilisateurs</h2>
                    <button class="btn-primary">
                        <i class="fas fa-plus mr-2"></i>Ajouter Utilisateur
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-6 border-b">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold">Liste des Utilisateurs</h3>
                            <div class="flex space-x-2">
                                <input type="text" placeholder="Rechercher..." class="px-3 py-2 border rounded-lg">
                                <select class="px-3 py-2 border rounded-lg">
                                    <option>Tous les rôles</option>
                                    <option>Admin</option>
                                    <option>User</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full" id="usersTable">
                            <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Utilisateur</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Rôle</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Statut</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200" id="usersTableBody">
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>Chargement des utilisateurs...
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Routes Management Section -->
            <div id="routes" class="section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Gestion des Trajets</h2>
                    <button class="btn-primary" onclick="showAddRouteForm()">
                        <i class="fas fa-plus mr-2"></i>Ajouter Trajet
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-6 border-b">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold">Liste des Trajets</h3>
                            <div class="flex space-x-2">
                                <input type="text" placeholder="Rechercher..." class="px-3 py-2 border rounded-lg" oninput="searchRoutes(this.value)">
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full" id="routesTable">
                            <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Trajet</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Gare Départ</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Gare Arrivée</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Distance</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200" id="routesTableBody">
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>Chargement des trajets...
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Stations Management Section -->
            <div id="stations" class="section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Gestion des Gares</h2>
                    <button class="btn-primary" onclick="showAddStationForm()">
                        <i class="fas fa-plus mr-2"></i>Ajouter Gare
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-6 border-b">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold">Liste des Gares</h3>
                            <div class="flex space-x-2">
                                <input type="text" placeholder="Rechercher..." class="px-3 py-2 border rounded-lg" oninput="searchStations(this.value)">
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full" id="stationsTable">
                            <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nom</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Ville</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Code</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200" id="stationsTableBody">
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>Chargement des gares...
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Trips Management Section -->
            <div id="trips" class="section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Gestion des Voyages</h2>
                    <button class="btn-primary" onclick="showAddTripForm()">
                        <i class="fas fa-plus mr-2"></i>Ajouter Voyage
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-6 border-b">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold">Liste des Voyages</h3>
                            <div class="flex space-x-2">
                                <input type="text" placeholder="Rechercher..." class="px-3 py-2 border rounded-lg" oninput="searchTrips(this.value)">
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full" id="tripsTable">
                            <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Numéro Voyage</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Trajet</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date Départ</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Places Disponibles</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200" id="tripsTableBody">
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>Chargement des voyages...
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Seat Availability Section -->
            <div id="seat-availability" class="section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Disponibilité des Places</h2>
                    <button class="btn-primary" onclick="refreshSeatAvailability()">
                        <i class="fas fa-sync-alt mr-2"></i>Rafraîchir
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-6 border-b">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold">Disponibilité des Places par Voyage</h3>
                            <div class="flex space-x-2">
                                <input type="text" placeholder="Rechercher voyage..." class="px-3 py-2 border rounded-lg" oninput="searchSeatAvailability(this.value)">
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full" id="seatAvailabilityTable">
                            <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Voyage</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Trajet</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Places Totales</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Places Disponibles</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200" id="seatAvailabilityTableBody">
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>Chargement des disponibilités...
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Payments Management Section -->
            <div id="payments" class="section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Gestion des Paiements</h2>
                    <button class="btn-primary" onclick="exportPayments()">
                        <i class="fas fa-download mr-2"></i>Exporter Paiements
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-6 border-b">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold">Liste des Paiements</h3>
                            <div class="flex space-x-2">
                                <input type="text" placeholder="Rechercher..." class="px-3 py-2 border rounded-lg" oninput="searchPayments(this.value)">
                                <select class="px-3 py-2 border rounded-lg">
                                    <option>Tous les statuts</option>
                                    <option>Complété</option>
                                    <option>En attente</option>
                                    <option>Échoué</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full" id="paymentsTable">
                            <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID Paiement</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Utilisateur</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Montant</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Statut</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200" id="paymentsTableBody">
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>Chargement des paiements...
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Cancellations and Refunds Section -->
            <div id="cancellations" class="section">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Annulations & Remboursements</h2>
                    <button class="btn-primary" onclick="exportCancellations()">
                        <i class="fas fa-download mr-2"></i>Exporter
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="p-6 border-b">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold">Demandes d'Annulation</h3>
                            <div class="flex space-x-2">
                                <input type="text" placeholder="Rechercher..." class="px-3 py-2 border rounded-lg" oninput="searchCancellations(this.value)">
                                <select class="px-3 py-2 border rounded-lg">
                                    <option>Tous les statuts</option>
                                    <option>En attente</option>
                                    <option>Approuvé</option>
                                    <option>Rejeté</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full" id="cancellationsTable">
                            <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID Demande</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Utilisateur</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Voyage</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Statut</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Montant Remboursé</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200" id="cancellationsTableBody">
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>Chargement des demandes...
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics" class="section">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">Analytiques Avancées</h2>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-4">Tendance des Réservations</h3>
                        <canvas id="trendChart" width="400" height="300"></canvas>
                    </div>

                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-4">Répartition par Ligne</h3>
                        <canvas id="pieChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settings" class="section">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">Paramètres Système</h2>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-lg font-semibold mb-4">Configuration Générale</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Nom de l'application</label>
                                <input type="text" value="TrainTicket Pro" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Email de contact</label>
                                <input type="email" value="<EMAIL>" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>
                            <button class="btn-primary">Sauvegarder</button>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-lg font-semibold mb-4">Sécurité</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span>Authentification à deux facteurs</span>
                                <button class="bg-green-500 text-white px-3 py-1 rounded text-sm">Activé</button>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>Logs de sécurité</span>
                                <button class="bg-blue-500 text-white px-3 py-1 rounded text-sm">Voir</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logs Section -->
            <div id="logs" class="section">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">Logs Système</h2>

                <div class="bg-white rounded-lg shadow-lg">
                    <div class="p-6 border-b">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold">Activité Système</h3>
                            <div class="flex space-x-2">
                                <select class="px-3 py-2 border rounded-lg">
                                    <option>Tous les types</option>
                                    <option>Connexions</option>
                                    <option>Erreurs</option>
                                    <option>Modifications</option>
                                </select>
                                <button class="btn-primary">Exporter</button>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <div class="space-y-3">
                            <div class="flex items-center justify-between py-2 border-b">
                                <div class="flex items-center">
                                    <span class="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                                    <span class="text-sm">Connexion réussie - admin</span>
                                </div>
                                <span class="text-sm text-gray-500">2024-01-15 14:30:25</span>
                            </div>
                            <div class="flex items-center justify-between py-2 border-b">
                                <div class="flex items-center">
                                    <span class="w-3 h-3 bg-blue-500 rounded-full mr-3"></span>
                                    <span class="text-sm">Nouvel utilisateur créé - marie.dubois</span>
                                </div>
                                <span class="text-sm text-gray-500">2024-01-15 14:25:12</span>
                            </div>
                            <div class="flex items-center justify-between py-2 border-b">
                                <div class="flex items-center">
                                    <span class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></span>
                                    <span class="text-sm">Modification train TGV 8745</span>
                                </div>
                                <span class="text-sm text-gray-500">2024-01-15 14:20:08</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Navigation entre les sections
    function showSection(sectionId) {
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
        });
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });
        document.getElementById(sectionId).classList.add('active');
        document.querySelector(`[onclick="showSection('${sectionId}')"]`).classList.add('active');

        if (sectionId === 'dashboard') {
            initDashboardCharts();
            loadDashboardStats();
        } else if (sectionId === 'analytics') {
            initAnalyticsCharts();
        } else if (sectionId === 'users') {
            loadUsers();
        } else if (sectionId === 'routes') {
            loadRoutes();
        } else if (sectionId === 'stations') {
            loadStations();
        } else if (sectionId === 'trips') {
            loadTrips();
        } else if (sectionId === 'seat-availability') {
            loadSeatAvailability();
        } else if (sectionId === 'payments') {
            loadPayments();
        } else if (sectionId === 'cancellations') {
            loadCancellations();
        }
    }

    // Initialiser les graphiques du dashboard
    function initDashboardCharts() {
        const reservationsCtx = document.getElementById('reservationsChart');
        if (reservationsCtx) {
            new Chart(reservationsCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
                    datasets: [{
                        label: 'Réservations',
                        data: [120, 190, 300, 500, 200, 300],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { display: false },
                    },
                    scales: { y: { beginAtZero: true } }
                }
            });
        }

        const revenueCtx = document.getElementById('revenueChart');
        if (revenueCtx) {
            new Chart(revenueCtx, {
                type: 'bar',
                data: {
                    labels: ['Paris-Lyon', 'Lyon-Marseille', 'Paris-Marseille', 'Marseille-Nice'],
                    datasets: [{
                        label: 'Revenus (€)',
                        data: [12000, 8500, 15000, 6000],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(245, 158, 11, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: { legend: { display: false } },
                    scales: { y: { beginAtZero: true } }
                }
            });
        }
    }

    // Initialiser les graphiques d'analytiques
    function initAnalyticsCharts() {
        const trendCtx = document.getElementById('trendChart');
        if (trendCtx) {
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
                    datasets: [{
                        label: 'Cette semaine',
                        data: [65, 59, 80, 81, 56, 55, 40],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    }, {
                        label: 'Semaine dernière',
                        data: [28, 48, 40, 19, 86, 27, 90],
                        borderColor: 'rgb(139, 92, 246)',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    }]
                },
                options: {
                    responsive: true,
                    interaction: { mode: 'index', intersect: false },
                    scales: { y: { beginAtZero: true } }
                }
            });
        }

        const pieCtx = document.getElementById('pieChart');
        if (pieCtx) {
            new Chart(pieCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Paris-Lyon', 'Lyon-Marseille', 'Paris-Marseille', 'Autres'],
                    datasets: [{
                        data: [35, 25, 30, 10],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(245, 158, 11, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: { legend: { position: 'bottom' } }
                }
            });
        }
    }

    // Charger les utilisateurs
    function loadUsers() {
        const tableBody = document.getElementById('usersTableBody');
        fetch('<%= request.getContextPath() %>/api/users?action=list')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayUsers(data.users);
                } else {
                    tableBody.innerHTML = `
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-red-500">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>Erreur: ${data.message}
                                </td>
                            </tr>
                        `;
                }
            })
            .catch(error => {
                console.error('Erreur lors du chargement des utilisateurs:', error);
                tableBody.innerHTML = `
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-red-500">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Erreur de connexion
                            </td>
                        </tr>
                    `;
            });
    }

    // Afficher les utilisateurs
    function displayUsers(users) {
        const tableBody = document.getElementById('usersTableBody');
        if (users.length === 0) {
            tableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            <i class="fas fa-users mr-2"></i>Aucun utilisateur trouvé
                        </td>
                    </tr>
                `;
            return;
        }

        let tableHTML = '';
        users.forEach(user => {
            const roleClass = user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800';
            const roleText = user.role === 'admin' ? 'Admin' : 'User';
            const statusClass = user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
            const statusText = user.status === 'active' ? 'Actif' : 'Inactif';
            const actionText = user.status === 'active' ? 'Désactiver' : 'Activer';
            const deleteButton = user.id !== 1 ?
                `<button onclick="deleteUser(${user.id})" class="text-red-600 hover:text-red-900">Supprimer</button>` :
                '<span class="text-gray-400">Protégé</span>';

            tableHTML += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <img class="h-10 w-10 rounded-full"
                                     src="https://ui-avatars.com/api/?name=" + encodeURIComponent(user.username) + "&background=3b82f6&color=fff"
                                     alt="` + user.username + `">
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">` + user.username + `</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">` + user.email + `</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ` + roleClass + `">` + roleText + `</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ` + statusClass + `">` + statusText + `</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">#` + user.id + `</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="toggleUserStatus(` + user.id + `)"
                                    class="text-indigo-600 hover:text-indigo-900 mr-3">
                                ` + actionText + `
                            </button>
                            ` + deleteButton + `
                        </td>
                    </tr>
                `;
        });

        tableBody.innerHTML = tableHTML;
    }

    // Charger les trajets
    function loadRoutes() {
        const tableBody = document.getElementById('routesTableBody');
        fetch('<%= request.getContextPath() %>/api/routes?action=list')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayRoutes(data.routes);
                } else {
                    tableBody.innerHTML = `
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-red-500">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>Erreur: ${data.message}
                                </td>
                            </tr>
                        `;
                }
            })
            .catch(error => {
                console.error('Erreur lors du chargement des trajets:', error);
                tableBody.innerHTML = `
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-red-500">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Erreur de connexion
                            </td>
                        </tr>
                    `;
            });
    }

    // Afficher les trajets
    function displayRoutes(routes) {
        const tableBody = document.getElementById('routesTableBody');
        if (routes.length === 0) {
            tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                            <i class="fas fa-route mr-2"></i>Aucun trajet trouvé
                        </td>
                    </tr>
                `;
            return;
        }

        let tableHTML = '';
        routes.forEach(route => {
            tableHTML += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${route.name}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${route.departureStation}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${route.arrivalStation}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${route.distance} km</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="editRoute(${route.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">Modifier</button>
                            <button onclick="deleteRoute(${route.id})" class="text-red-600 hover:text-red-900">Supprimer</button>
                        </td>
                    </tr>
                `;
        });
        tableBody.innerHTML = tableHTML;
    }

    // Charger les gares
    function loadStations() {
        const tableBody = document.getElementById('stationsTableBody');
        fetch('<%= request.getContextPath() %>/api/stations?action=list')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayStations(data.stations);
                } else {
                    tableBody.innerHTML = `
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center text-red-500">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>Erreur: ${data.message}
                                </td>
                            </tr>
                        `;
                }
            })
            .catch(error => {
                console.error('Erreur lors du chargement des gares:', error);
                tableBody.innerHTML = `
                        <tr>
                            <td colspan="4" class="px-6 py-4 text-center text-red-500">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Erreur de connexion
                            </td>
                        </tr>
                    `;
            });
    }

    // Afficher les gares
    function displayStations(stations) {
        const tableBody = document.getElementById('stationsTableBody');
        if (stations.length === 0) {
            tableBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                            <i class="fas fa-map-marker-alt mr-2"></i>Aucune gare trouvée
                        </td>
                    </tr>
                `;
            return;
        }

        let tableHTML = '';
        stations.forEach(station => {
            tableHTML += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${station.name}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${station.city}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${station.code}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="editStation(${station.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">Modifier</button>
                            <button onclick="deleteStation(${station.id})" class="text-red-600 hover:text-red-900">Supprimer</button>
                        </td>
                    </tr>
                `;
        });
        tableBody.innerHTML = tableHTML;
    }

    // Charger les voyages
    function loadTrips() {
        const tableBody = document.getElementById('tripsTableBody');
        fetch('<%= request.getContextPath() %>/api/trips?action=list')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayTrips(data.trips);
                } else {
                    tableBody.innerHTML = `
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-red-500">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>Erreur: ${data.message}
                                </td>
                            </tr>
                        `;
                }
            })
            .catch(error => {
                console.error('Erreur lors du chargement des voyages:', error);
                tableBody.innerHTML = `
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-red-500">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Erreur de connexion
                            </td>
                        </tr>
                    `;
            });
    }

    // Afficher les voyages
    function displayTrips(trips) {
        const tableBody = document.getElementById('tripsTableBody');
        if (trips.length === 0) {
            tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                            <i class="fas fa-train mr-2"></i>Aucun voyage trouvé
                        </td>
                    </tr>
                `;
            return;
        }

        let tableHTML = '';
        trips.forEach(trip => {
            tableHTML += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${trip.tripNumber}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${trip.route}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${trip.departureDate}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${trip.availableSeats}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="editTrip(${trip.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">Modifier</button>
                            <button onclick="deleteTrip(${trip.id})" class="text-red-600 hover:text-red-900">Supprimer</button>
                        </td>
                    </tr>
                `;
        });
        tableBody.innerHTML = tableHTML;
    }

    // Charger la disponibilité des places
    function loadSeatAvailability() {
        const tableBody = document.getElementById('seatAvailabilityTableBody');
        fetch('<%= request.getContextPath() %>/api/seat-availability?action=list')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displaySeatAvailability(data.seatData);
                } else {
                    tableBody.innerHTML = `
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-red-500">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>Erreur: ${data.message}
                                </td>
                            </tr>
                        `;
                }
            })
            .catch(error => {
                console.error('Erreur lors du chargement des disponibilités:', error);
                tableBody.innerHTML = `
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-red-500">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Erreur de connexion
                            </td>
                        </tr>
                    `;
            });
    }

    // Afficher la disponibilité des places
    function displaySeatAvailability(seatData) {
        const tableBody = document.getElementById('seatAvailabilityTableBody');
        if (seatData.length === 0) {
            tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                            <i class="fas fa-chair mr-2"></i>Aucune disponibilité trouvée
                        </td>
                    </tr>
                `;
            return;
        }

        let tableHTML = '';
        seatData.forEach(data => {
            tableHTML += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${data.tripNumber}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${data.route}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${data.totalSeats}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${data.availableSeats}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="updateSeatAvailability(${data.tripId})" class="text-indigo-600 hover:text-indigo-900">Mettre à jour</button>
                        </td>
                    </tr>
                `;
        });
        tableBody.innerHTML = tableHTML;
    }

    // Charger les paiements
    function loadPayments() {
        const tableBody = document.getElementById('paymentsTableBody');
        fetch('<%= request.getContextPath() %>/api/payments?action=list')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayPayments(data.payments);
                } else {
                    tableBody.innerHTML = `
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-red-500">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>Erreur: ${data.message}
                                </td>
                            </tr>
                        `;
                }
            })
            .catch(error => {
                console.error('Erreur lors du chargement des paiements:', error);
                tableBody.innerHTML = `
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-red-500">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Erreur de connexion
                            </td>
                        </tr>
                    `;
            });
    }

    // Afficher les paiements
    function displayPayments(payments) {
        const tableBody = document.getElementById('paymentsTableBody');
        if (payments.length === 0) {
            tableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            <i class="fas fa-credit-card mr-2"></i>Aucun paiement trouvé
                        </td>
                    </tr>
                `;
            return;
        }

        let tableHTML = '';
        payments.forEach(payment => {
            const statusClass = payment.status === 'completed' ? 'bg-green-100 text-green-800' :
                payment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800';
            const statusText = payment.status === 'completed' ? 'Complété' :
                payment.status === 'pending' ? 'En attente' : 'Échoué';
            tableHTML += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${payment.id}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${payment.username}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">€${payment.amount}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">${statusText}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${payment.date}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="viewPaymentDetails(${payment.id})" class="text-indigo-600 hover:text-indigo-900">Détails</button>
                        </td>
                    </tr>
                `;
        });
        tableBody.innerHTML = tableHTML;
    }

    // Charger les annulations
    function loadCancellations() {
        const tableBody = document.getElementById('cancellationsTableBody');
        fetch('<%= request.getContextPath() %>/api/cancellations?action=list')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayCancellations(data.cancellations);
                } else {
                    tableBody.innerHTML = `
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-red-500">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>Erreur: ${data.message}
                                </td>
                            </tr>
                        `;
                }
            })
            .catch(error => {
                console.error('Erreur lors du chargement des annulations:', error);
                tableBody.innerHTML = `
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-red-500">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Erreur de connexion
                            </td>
                        </tr>
                    `;
            });
    }

    // Afficher les annulations
    function displayCancellations(cancellations) {
        const tableBody = document.getElementById('cancellationsTableBody');
        if (cancellations.length === 0) {
            tableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            <i class="fas fa-times-circle mr-2"></i>Aucune demande d'annulation trouvée
                        </td>
                    </tr>
                `;
            return;
        }

        let tableHTML = '';
        cancellations.forEach(cancellation => {
            const statusClass = cancellation.status === 'approved' ? 'bg-green-100 text-green-800' :
                cancellation.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800';
            const statusText = cancellation.status === 'approved' ? 'Approuvé' :
                cancellation.status === 'pending' ? 'En attente' : 'Rejeté';
            tableHTML += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${cancellation.id}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${cancellation.username}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${cancellation.tripNumber}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">${statusText}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">€${cancellation.refundedAmount}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="approveCancellation(${cancellation.id})" class="text-green-600 hover:text-green-900 mr-3">Approuver</button>
                            <button onclick="rejectCancellation(${cancellation.id})" class="text-red-600 hover:text-red-900">Rejeter</button>
                        </td>
                    </tr>
                `;
        });
        tableBody.innerHTML = tableHTML;
    }

    // Fonctions pour les actions
    function showAddRouteForm() {
        alert('Formulaire d\'ajout de trajet à implémenter');
        // Implémenter un formulaire modal pour ajouter un trajet
    }

    function editRoute(routeId) {
        alert(`Modifier le trajet ID: ${routeId}`);
        // Implémenter un formulaire modal pour modifier le trajet
    }

    function deleteRoute(routeId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer ce trajet ?')) {
            fetch('<%= request.getContextPath() %>/api/routes', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=delete&routeId=${routeId}`
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Trajet supprimé avec succès', 'success');
                        loadRoutes();
                    } else {
                        showNotification('Erreur: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showNotification('Erreur de connexion', 'error');
                });
        }
    }

    function showAddStationForm() {
        alert('Formulaire d\'ajout de gare à implémenter');
        // Implémenter un formulaire modal pour ajouter une gare
    }

    function editStation(stationId) {
        alert(`Modifier la gare ID: ${stationId}`);
        // Implémenter un formulaire modal pour modifier la gare
    }

    function deleteStation(stationId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette gare ?')) {
            fetch('<%= request.getContextPath() %>/api/stations', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=delete&stationId=${stationId}`
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Gare supprimée avec succès', 'success');
                        loadStations();
                    } else {
                        showNotification('Erreur: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showNotification('Erreur de connexion', 'error');
                });
        }
    }

    function showAddTripForm() {
        alert('Formulaire d\'ajout de voyage à implémenter');
        // Implémenter un formulaire modal pour ajouter un voyage
    }

    function editTrip(tripId) {
        alert(`Modifier le voyage ID: ${tripId}`);
        // Implémenter un formulaire modal pour modifier le voyage
    }

    function deleteTrip(tripId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer ce voyage ?')) {
            fetch('<%= request.getContextPath() %>/api/trips', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=delete&tripId=${tripId}`
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Voyage supprimé avec succès', 'success');
                        loadTrips();
                    } else {
                        showNotification('Erreur: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showNotification('Erreur de connexion', 'error');
                });
        }
    }

    function updateSeatAvailability(tripId) {
        alert(`Mettre à jour la disponibilité pour le voyage ID: ${tripId}`);
        // Implémenter un formulaire modal pour ajuster les places
    }

    function refreshSeatAvailability() {
        loadSeatAvailability();
        showNotification('Données de disponibilité rafraîchies', 'success');
    }

    function viewPaymentDetails(paymentId) {
        alert(`Afficher les détails du paiement ID: ${paymentId}`);
        // Implémenter un modal pour afficher les détails du paiement
    }

    function exportPayments() {
        alert('Exportation des paiements en cours...');
        // Implémenter l'exportation des paiements
    }

    function approveCancellation(cancellationId) {
        if (confirm('Approuver cette demande d\'annulation ?')) {
            fetch('<%= request.getContextPath() %>/api/cancellations', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=approve&cancellationId=${cancellationId}`
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Demande approuvée avec succès', 'success');
                        loadCancellations();
                    } else {
                        showNotification('Erreur: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showNotification('Erreur de connexion', 'error');
                });
        }
    }

    function rejectCancellation(cancellationId) {
        if (confirm('Rejeter cette demande d\'annulation ?')) {
            fetch('<%= request.getContextPath() %>/api/cancellations', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=reject&cancellationId=${cancellationId}`
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Demande rejetée avec succès', 'success');
                        loadCancellations();
                    } else {
                        showNotification('Erreur: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showNotification('Erreur de connexion', 'error');
                });
        }
    }

    function exportCancellations() {
        alert('Exportation des annulations en cours...');
        // Implémenter l'exportation des annulations
    }

    // Charger les statistiques
    function loadDashboardStats() {
        fetch('<%= request.getContextPath() %>/api/users?action=stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateDashboardStats(data.stats);
                }
            })
            .catch(error => {
                console.error('Erreur lors du chargement des statistiques:', error);
            });
    }

    function updateDashboardStats(stats) {
        const totalUsersElement = document.getElementById('totalUsers');
        if (totalUsersElement) {
            animateNumber(totalUsersElement, stats.totalUsers);
        }
    }

    function animateNumber(element, target) {
        let current = 0;
        const increment = target / 50;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 50);
    }

  

        // Mise à jour en temps réel (simulation)
        function updateRealTimeData() {
            // Simuler des mises à jour de données
            setInterval(() => {
                const users = document.getElementById('totalUsers');
                const reservations = document.getElementById('totalReservations');

                if (users && Math.random() > 0.7) {
                    const currentUsers = parseInt(users.textContent);
                    users.textContent = currentUsers + 1;
                }

                if (reservations && Math.random() > 0.8) {
                    const currentReservations = parseInt(reservations.textContent.replace(',', ''));
                    reservations.textContent = (currentReservations + 1).toLocaleString();
                }
            }, 30000); // Mise à jour toutes les 30 secondes
        }

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            // Initialiser les graphiques du dashboard
            initDashboardCharts();

            // Animer les statistiques
            setTimeout(animateStats, 500);

            // Démarrer les mises à jour en temps réel
            updateRealTimeData();

            // Gestion du menu mobile
            const mobileMenuBtn = document.querySelector('.md\\:hidden button');
            const sidebar = document.querySelector('.sidebar');

            if (mobileMenuBtn && sidebar) {
                mobileMenuBtn.addEventListener('click', () => {
                    sidebar.classList.toggle('-translate-x-full');
                });
            }
        });

        // Fonction de recherche
        function searchUsers(query) {
            // Implémentation de la recherche d'utilisateurs
            console.log('Recherche:', query);
        }

        // Fonction d'export des logs
        function exportLogs() {
            // Implémentation de l'export des logs
            alert('Export des logs en cours...');
        }

        // Changer le statut d'un utilisateur
        function toggleUserStatus(userId) {
            if (confirm('Êtes-vous sûr de vouloir changer le statut de cet utilisateur ?')) {
                fetch('<%= request.getContextPath() %>/api/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=toggle-status&userId=${userId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Statut mis à jour avec succès', 'success');
                        loadUsers(); // Recharger la liste
                    } else {
                        showNotification('Erreur: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showNotification('Erreur de connexion', 'error');
                });
            }
        }

        // Supprimer un utilisateur
        function deleteUser(userId) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ? Cette action est irréversible.')) {
                fetch('<%= request.getContextPath() %>/api/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=delete&userId=${userId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Utilisateur supprimé avec succès', 'success');
                        loadUsers(); // Recharger la liste
                        loadDashboardStats(); // Mettre à jour les stats
                    } else {
                        showNotification('Erreur: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showNotification('Erreur de connexion', 'error');
                });
            }
        }

        // Notifications en temps réel
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            let bgColor = 'bg-blue-500';
            if (type === 'success') bgColor = 'bg-green-500';
            else if (type === 'error') bgColor = 'bg-red-500';
            else if (type === 'warning') bgColor = 'bg-yellow-500';

            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${bgColor} text-white`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Simuler des notifications
        setInterval(() => {
            const notifications = [
                'Nouvelle réservation reçue',
                'Utilisateur connecté',
                'Maintenance programmée',
                'Rapport généré'
            ];

            if (Math.random() > 0.9) {
                const message = notifications[Math.floor(Math.random() * notifications.length)];
                showNotification(message, 'info');
            }
        }, 60000); // Notification toutes les minutes (si aléatoire)
    </script>
</body>
</html>