<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page import="Modal.User" %>

<%
    User user = (session != null) ? (User) session.getAttribute("user") : null;
%>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>TrainTicket - Réservez Vos Billets de Train</title>
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <style>
        /* Ton style CSS personnalisé ici (comme dans ton code original) */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f9fafb;
            margin: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .navbar {
            background-color: #2563eb;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 50;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
        }
        .navbar-brand {
            font-size: 1.75rem;
            font-weight: 700;
            color: #ffffff;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
            text-decoration: none;
        }
        .navbar-brand:hover {
            color: #dbeafe;
        }
        .nav-link {
            color: #ffffff;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: background-color 0.3s ease, color 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        .nav-link:hover {
            color: #dbeafe;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        /* Hero Section */
        .hero-section {
            position: relative;
            height: 90vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            padding: 0 1rem;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            opacity: 0.3;
        }

        .hero-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        }
        .hero-content {
            position: relative;
            z-index: 5;
            max-width: 4xl;
            padding: 0 1rem;
        }

        .hero-content h1 {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            animation: fadeIn 1.5s ease-in-out;
            line-height: 1.1;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .hero-content p {
            font-size: 1.5rem;
            margin-bottom: 3rem;
            animation: fadeIn 2s ease-in-out;
            opacity: 0.95;
            line-height: 1.6;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-content .cta-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 1rem 2.5rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
            font-size: 1.1rem;
            min-width: 220px;
        }

        .hero-content .cta-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
            color: white;
            text-decoration: none;
        }

        .hero-content .cta-btn.secondary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .hero-content .cta-btn.secondary:hover {
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
        /* Features Section */
        .features-section {
            background: #f8fafc;
            padding: 6rem 0;
            position: relative;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid #f1f5f9;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 70px;
            height: 70px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.75rem;
            margin: 0 auto 1.5rem;
            color: white;
        }

        .feature-icon.green {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .feature-icon.blue {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        .feature-icon.purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        /* Search Section */
        .search-container {
            max-width: 1200px;
            margin: -10rem auto 8rem auto;
            z-index: 10;
            position: relative;
            padding: 0 1rem;
        }

        .search-form {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 3rem;
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
            align-items: flex-end;
            animation: slideUp 0.8s ease-out;
        }
        .search-form h2 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            margin-bottom: 2rem;
            width: 100%;
        }

        .form-group {
            flex: 1;
            min-width: 250px;
            position: relative;
        }

        .form-group label {
            display: block;
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
        }

        .form-group label i {
            margin-right: 0.5rem;
            color: #10b981;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 1rem;
            color: #1f2937;
            transition: all 0.3s ease;
            background: #f9fafb;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #10b981;
            background: white;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        .submit-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 1rem 2.5rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 200px;
            border: none;
            font-size: 1.1rem;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }
        /* About Us Section */
        .about-section {
            max-width: 1200px;
            margin: 4rem auto;
            padding: 0 1.5rem;
            text-align: center;
        }
        .about-section h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1.5rem;
        }
        .about-section p {
            font-size: 1.1rem;
            color: #4b5563;
            line-height: 1.8;
            max-width: 800px;
            margin: 0 auto 2rem;
        }
        .about-section .cta-btn {
            background-color: #2563eb;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            transition: background-color 0.3s, transform 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        .about-section .cta-btn:hover {
            background-color: #1d4ed8;
            transform: translateY(-3px);
        }
        /* Footer */
        .footer {
            background-color: #2563eb;
            color: white;
            padding: 2rem 0;
            text-align: center;
            margin-top: auto;
        }
        .footer-links a {
            color: #dbeafe;
            margin: 0 1rem;
            font-weight: 500;
            transition: color 0.3s ease;
            text-decoration: none;
        }
        .footer-links a:hover {
            color: #ffffff;
        }
        .footer .social-icons a {
            color: #ffffff;
            font-size: 1.5rem;
            margin: 0 0.75rem;
            transition: color 0.3s ease;
        }
        .footer .social-icons a:hover {
            color: #f59e0b;
        }
        /* Animations */
        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        @keyframes slideUp {
            0% { opacity: 0; transform: translateY(40px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }
            .form-group {
                min-width: 100%;
            }
            .hero-content h1 {
                font-size: 2.5rem;
            }
            .hero-content p {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
<!-- Navbar -->
<nav class="navbar" role="navigation" aria-label="main navigation">
    <a href="#" class="navbar-brand" aria-label="TrainTicket Home">
        <i class="fas fa-train mr-2"></i> TrainTicket
    </a>
    <div class="flex space-x-6 items-center">
        <a href="views/search-trains.jsp" class="nav-link">
            <i class="fas fa-search mr-1"></i>Rechercher
        </a>
        <a href="#search" class="nav-link">Réservation</a>
        <a href="#about" class="nav-link">À propos</a>
        <a href="#" class="nav-link">Contact</a>

        <% if (user != null) { %>
        <!-- Menu utilisateur connecté -->
        <div class="flex items-center space-x-4 ml-4 pl-4 border-l border-blue-300">
            <div class="flex items-center space-x-2">
                <i class="fas fa-user-circle text-white text-xl"></i>
                <span class="text-white font-medium">Bonjour, <%= user.getUsername() %></span>
            </div>

            <div class="flex space-x-2">
                <% if (user.getRole().equals("admin")) { %>
                <a href="${pageContext.request.contextPath}/views/home.jsp" class="nav-link bg-blue-600 rounded-lg px-3 py-1">
                    <i class="fas fa-tachometer-alt mr-1"></i> Dashboard
                </a>
                <% } else { %>
                <a href="${pageContext.request.contextPath}/views/myBookings.jsp" class="nav-link bg-blue-600 rounded-lg px-3 py-1">
                    <i class="fas fa-ticket-alt mr-1"></i> Mes Réservations
                </a>
                <% } %>

                <a href="${pageContext.request.contextPath}/logout" class="nav-link bg-red-600 hover:bg-red-700 rounded-lg px-3 py-1">
                    <i class="fas fa-sign-out-alt mr-1"></i> Déconnexion
                </a>
            </div>
        </div>
        <% } else { %>
        <!-- Menu utilisateur non connecté -->
        <div class="flex space-x-2 ml-4 pl-4 border-l border-blue-300">
            <a href="${pageContext.request.contextPath}/views/login.jsp" class="nav-link bg-green-600 hover:bg-green-700 rounded-lg px-3 py-1">
                <i class="fas fa-sign-in-alt mr-1"></i> Connexion
            </a>
            <a href="${pageContext.request.contextPath}/views/register.jsp" class="nav-link bg-yellow-600 hover:bg-yellow-700 rounded-lg px-3 py-1">
                <i class="fas fa-user-plus mr-1"></i> Inscription
            </a>
        </div>
        <% } %>
    </div>
</nav>

<!-- Hero Section -->
<section class="hero-section" role="banner" aria-label="Présentation du site TrainTicket">
    <div class="hero-content max-w-4xl px-4">
        <% if (user != null) { %>
        <h1>Bon retour, <%= user.getUsername() %> !</h1>
        <p>Prêt pour votre prochain voyage ? Découvrez nos trajets rapides, confortables et abordables partout en France.</p>
        <% } else { %>
        <h1>Réservez Vos Billets de Train en Toute Simplicité</h1>
        <p>Découvrez des trajets rapides, confortables et abordables partout en France.</p>
        <% } %>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="views/search-trains.jsp" class="cta-btn" role="button" aria-describedby="cta-desc">
                <i class="fas fa-search mr-2"></i>Rechercher un train
            </a>
            <% if (user == null) { %>
            <a href="views/login.jsp" class="cta-btn secondary" role="button">
                <i class="fas fa-sign-in-alt mr-2"></i>Se connecter
            </a>
            <% } %>
        </div>
        <span id="cta-desc" class="sr-only">Lien vers la section de réservation</span>
    </div>
</section>

<!-- Features Section -->
<section class="features-section">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">Pourquoi choisir TrainApp ?</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Découvrez une nouvelle façon de voyager avec notre plateforme moderne et intuitive
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="feature-card">
                <div class="feature-icon green">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Recherche Simplifiée</h3>
                <p class="text-gray-600 leading-relaxed">
                    Trouvez facilement vos trajets avec notre moteur de recherche intelligent.
                    Filtrez par date, ville et disponibilité en quelques clics.
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon blue">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Réservation Rapide</h3>
                <p class="text-gray-600 leading-relaxed">
                    Réservez vos billets en moins de 2 minutes. Interface intuitive
                    et processus de paiement sécurisé pour une expérience optimale.
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon purple">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Sécurité Garantie</h3>
                <p class="text-gray-600 leading-relaxed">
                    Vos données sont protégées par un cryptage de niveau bancaire.
                    Paiements sécurisés et gestion transparente de vos réservations.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Search Form Section -->
<section id="search" class="search-container" aria-labelledby="search-title">
    <form id="quickSearchForm" class="search-form" role="search" aria-describedby="search-desc">
        <h2 id="search-title">Recherche Rapide</h2>
        <p id="search-desc" class="sr-only">Formulaire de recherche rapide pour billets de train avec sélection de ville départ, ville arrivée et date.</p>

        <div class="form-group">
            <label for="departureCity"><i class="fas fa-location-arrow"></i> Ville de départ</label>
            <input type="text" id="departureCity" name="departureCity" placeholder="Ex: Paris" required aria-required="true">
        </div>

        <div class="form-group">
            <label for="destinationCity"><i class="fas fa-map-marker-alt"></i> Ville d'arrivée</label>
            <input type="text" id="destinationCity" name="destinationCity" placeholder="Ex: Lyon" required aria-required="true">
        </div>

        <div class="form-group">
            <label for="date"><i class="fas fa-calendar-alt"></i> Date du voyage</label>
            <input type="date" id="date" name="date" required aria-required="true"
                   min="<%= new java.text.SimpleDateFormat("yyyy-MM-dd").format(new java.util.Date()) %>" />
        </div>

        <button type="submit" class="submit-btn" aria-label="Rechercher billets de train">
            <i class="fas fa-search mr-2"></i> Rechercher
        </button>
    </form>

    <div class="text-center mt-8">
        <p class="text-gray-600 mb-4">Ou utilisez notre recherche avancée pour plus d'options</p>
        <a href="views/search-trains.jsp" class="inline-flex items-center text-green-600 hover:text-green-700 font-semibold transition-colors duration-200">
            <i class="fas fa-search-plus mr-2"></i>
            Recherche Avancée
            <i class="fas fa-arrow-right ml-2"></i>
        </a>
    </div>
</section>

<script>
document.getElementById('quickSearchForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const departureCity = document.getElementById('departureCity').value.trim();
    const destinationCity = document.getElementById('destinationCity').value.trim();
    const date = document.getElementById('date').value;

    if (!departureCity || !destinationCity || !date) {
        alert('Veuillez remplir tous les champs');
        return;
    }

    if (departureCity.toLowerCase() === destinationCity.toLowerCase()) {
        alert('La ville de départ et la destination doivent être différentes');
        return;
    }

    // Rediriger vers la page de recherche avec les paramètres
    const searchUrl = 'views/search-trains.jsp?departureCity=' + encodeURIComponent(departureCity) +
                     '&destinationCity=' + encodeURIComponent(destinationCity) +
                     '&travelDate=' + encodeURIComponent(date);
    window.location.href = searchUrl;
});
</script>

<!-- About Section -->
<section id="about" class="about-section" aria-labelledby="about-title" tabindex="-1">
    <h2 id="about-title">À propos de TrainTicket</h2>
    <p>
        TrainTicket est votre plateforme de confiance pour réserver des billets de train
        facilement et rapidement. Nous offrons un large choix de trajets, des tarifs compétitifs
        et un service client à votre écoute.
    </p>

</section>

<!-- Footer -->
<footer class="footer" role="contentinfo">
    <p>&copy; 2025 TrainTicket. Tous droits réservés.</p>
    <div class="footer-links" role="navigation" aria-label="Liens de pied de page">
        <a href="#">Politique de confidentialité</a>
        <a href="#">Conditions d'utilisation</a>
        <a href="#">Contact</a>
    </div>
    <div class="social-icons" aria-label="Réseaux sociaux">
        <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
    </div>
</footer>
</body>
</html>