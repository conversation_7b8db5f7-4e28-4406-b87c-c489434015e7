<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page import="Modal.User" %>

<%
    User user = (session != null) ? (User) session.getAttribute("user") : null;
%>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>TrainTicket - Réservez Vos Billets de Train</title>
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <style>
        /* Ton style CSS personnalisé ici (comme dans ton code original) */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f9fafb;
            margin: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .navbar {
            background-color: #2563eb;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 50;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
        }
        .navbar-brand {
            font-size: 1.75rem;
            font-weight: 700;
            color: #ffffff;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
            text-decoration: none;
        }
        .navbar-brand:hover {
            color: #dbeafe;
        }
        .nav-link {
            color: #ffffff;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: background-color 0.3s ease, color 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        .nav-link:hover {
            color: #dbeafe;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        /* Hero Section */
        .hero-section {
            position: relative;
            height: 80vh;
            background-image: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)),
            url('https://images.unsplash.com/photo-1515621061946-eff1c2a352bd?q=80&w=2070&auto=format&fit=crop');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            padding: 0 1rem;
        }
        .hero-content h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            animation: fadeIn 1.5s ease-in-out;
        }
        .hero-content p {
            font-size: 1.5rem;
            margin-bottom: 2.5rem;
            animation: fadeIn 2s ease-in-out;
        }
        .hero-content .cta-btn {
            background-color: #f59e0b;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            transition: background-color 0.3s, transform 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        .hero-content .cta-btn:hover {
            background-color: #d97706;
            transform: translateY(-3px);
        }
        /* Search Section */
        .search-container {
            max-width: 1000px;
            margin: -8rem auto 6rem auto;
            z-index: 10;
            position: relative;
        }
        .search-form {
            background-color: white;
            padding: 2.5rem;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            align-items: flex-end;
            animation: slideUp 0.8s ease-out;
        }
        .search-form h2 {
            font-size: 2rem;
            font-weight: 700;
            color: #2563eb;
            text-align: center;
            margin-bottom: 1.5rem;
            width: 100%;
        }
        .form-group {
            flex: 1;
            min-width: 220px;
            position: relative;
        }
        .form-group label {
            display: block;
            font-size: 0.9rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 0.85rem 1rem 0.85rem 2.75rem;
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            font-size: 1rem;
            color: #1f2937;
            transition: border-color 0.3s, box-shadow 0.3s;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
        }
        .form-group i {
            position: absolute;
            left: 0.85rem;
            top: 2.85rem;
            color: #6b7280;
            transition: color 0.3s;
        }
        .form-group input:focus + i, .form-group select:focus + i {
            color: #2563eb;
        }
        .submit-btn {
            background-color: #2563eb;
            color: white;
            padding: 0.85rem 2.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: background-color 0.3s, transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            min-width: 160px;
            border: none;
        }
        .submit-btn:hover {
            background-color: #1d4ed8;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(29, 78, 216, 0.3);
        }
        /* About Us Section */
        .about-section {
            max-width: 1200px;
            margin: 4rem auto;
            padding: 0 1.5rem;
            text-align: center;
        }
        .about-section h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1.5rem;
        }
        .about-section p {
            font-size: 1.1rem;
            color: #4b5563;
            line-height: 1.8;
            max-width: 800px;
            margin: 0 auto 2rem;
        }
        .about-section .cta-btn {
            background-color: #2563eb;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            transition: background-color 0.3s, transform 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        .about-section .cta-btn:hover {
            background-color: #1d4ed8;
            transform: translateY(-3px);
        }
        /* Footer */
        .footer {
            background-color: #2563eb;
            color: white;
            padding: 2rem 0;
            text-align: center;
            margin-top: auto;
        }
        .footer-links a {
            color: #dbeafe;
            margin: 0 1rem;
            font-weight: 500;
            transition: color 0.3s ease;
            text-decoration: none;
        }
        .footer-links a:hover {
            color: #ffffff;
        }
        .footer .social-icons a {
            color: #ffffff;
            font-size: 1.5rem;
            margin: 0 0.75rem;
            transition: color 0.3s ease;
        }
        .footer .social-icons a:hover {
            color: #f59e0b;
        }
        /* Animations */
        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        @keyframes slideUp {
            0% { opacity: 0; transform: translateY(40px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }
            .form-group {
                min-width: 100%;
            }
            .hero-content h1 {
                font-size: 2.5rem;
            }
            .hero-content p {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
<!-- Navbar -->
<nav class="navbar" role="navigation" aria-label="main navigation">
    <a href="#" class="navbar-brand" aria-label="TrainTicket Home">
        <i class="fas fa-train mr-2"></i> TrainTicket
    </a>
    <div class="flex space-x-6 items-center">
        <a href="#search" class="nav-link">Réservation</a>
        <a href="#about" class="nav-link">À propos</a>
        <a href="#" class="nav-link">Contact</a>

        <% if (user != null) { %>
        <!-- Menu utilisateur connecté -->
        <div class="flex items-center space-x-4 ml-4 pl-4 border-l border-blue-300">
            <div class="flex items-center space-x-2">
                <i class="fas fa-user-circle text-white text-xl"></i>
                <span class="text-white font-medium">Bonjour, <%= user.getUsername() %></span>
            </div>

            <div class="flex space-x-2">
                <% if (user.getRole().equals("admin")) { %>
                <a href="${pageContext.request.contextPath}/views/home.jsp" class="nav-link bg-blue-600 rounded-lg px-3 py-1">
                    <i class="fas fa-tachometer-alt mr-1"></i> Dashboard
                </a>
                <% } else { %>
                <a href="${pageContext.request.contextPath}/views/myBookings.jsp" class="nav-link bg-blue-600 rounded-lg px-3 py-1">
                    <i class="fas fa-ticket-alt mr-1"></i> Mes Réservations
                </a>
                <% } %>

                <a href="${pageContext.request.contextPath}/logout" class="nav-link bg-red-600 hover:bg-red-700 rounded-lg px-3 py-1">
                    <i class="fas fa-sign-out-alt mr-1"></i> Déconnexion
                </a>
            </div>
        </div>
        <% } else { %>
        <!-- Menu utilisateur non connecté -->
        <div class="flex space-x-2 ml-4 pl-4 border-l border-blue-300">
            <a href="${pageContext.request.contextPath}/views/login.jsp" class="nav-link bg-green-600 hover:bg-green-700 rounded-lg px-3 py-1">
                <i class="fas fa-sign-in-alt mr-1"></i> Connexion
            </a>
            <a href="${pageContext.request.contextPath}/views/register.jsp" class="nav-link bg-yellow-600 hover:bg-yellow-700 rounded-lg px-3 py-1">
                <i class="fas fa-user-plus mr-1"></i> Inscription
            </a>
        </div>
        <% } %>
    </div>
</nav>

<!-- Hero Section -->
<section class="hero-section" role="banner" aria-label="Présentation du site TrainTicket">
    <div class="hero-content max-w-4xl px-4">
        <% if (user != null) { %>
        <h1>Bon retour, <%= user.getUsername() %> !</h1>
        <p>Prêt pour votre prochain voyage ? Découvrez nos trajets rapides, confortables et abordables partout en France.</p>
        <% } else { %>
        <h1>Réservez Vos Billets de Train en Toute Simplicité</h1>
        <p>Découvrez des trajets rapides, confortables et abordables partout en France.</p>
        <% } %>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="views/search-trains.jsp" class="cta-btn" role="button" aria-describedby="cta-desc">
                <i class="fas fa-search mr-2"></i>Rechercher un train
            </a>
            <% if (user == null) { %>
            <a href="views/login.jsp" class="cta-btn bg-blue-600 hover:bg-blue-700" role="button">
                <i class="fas fa-sign-in-alt mr-2"></i>Se connecter
            </a>
            <% } %>
        </div>
        <span id="cta-desc" class="sr-only">Lien vers la section de réservation</span>
    </div>
</section>

<!-- Search Form Section -->
<section id="search" class="search-container" aria-labelledby="search-title">
    <form action="searchTrip" method="POST" class="search-form" role="search" aria-describedby="search-desc">
        <h2 id="search-title">Trouvez Votre Billet</h2>
        <p id="search-desc" class="sr-only">Formulaire de recherche pour billets de train avec sélection de ville départ, ville arrivée et date.</p>
        <div class="form-group">
            <label for="departureCity"><i class="fas fa-location-arrow"></i> Ville de départ</label>
            <select id="departureCity" name="departureCity" required aria-required="true">
                <option value="">-- Sélectionnez la ville de départ --</option>
                <option value="Paris">Paris</option>
                <option value="Lyon">Lyon</option>
                <option value="Marseille">Marseille</option>
                <option value="Bordeaux">Bordeaux</option>
                <option value="Strasbourg">Strasbourg</option>
            </select>
        </div>
        <div class="form-group">
            <label for="destinationCity"><i class="fas fa-map-marker-alt"></i> Ville d'arrivée</label>
            <select id="destinationCity" name="destinationCity" required aria-required="true">
                <option value="">-- Sélectionnez la ville d'arrivée --</option>
                <option value="Paris">Paris</option>
                <option value="Lyon">Lyon</option>
                <option value="Marseille">Marseille</option>
                <option value="Bordeaux">Bordeaux</option>
                <option value="Strasbourg">Strasbourg</option>
            </select>
        </div>
        <div class="form-group">
            <label for="date"><i class="fas fa-calendar-alt"></i> Date du voyage</label>
            <input type="date" id="date" name="date" required aria-required="true"
                   min="<%= new java.text.SimpleDateFormat("yyyy-MM-dd").format(new java.util.Date()) %>" />
        </div>
        <button type="submit" class="submit-btn" aria-label="Rechercher billets de train">
            <i class="fas fa-search mr-2"></i> Rechercher
        </button>
    </form>
</section>

<!-- About Section -->
<section id="about" class="about-section" aria-labelledby="about-title" tabindex="-1">
    <h2 id="about-title">À propos de TrainTicket</h2>
    <p>
        TrainTicket est votre plateforme de confiance pour réserver des billets de train
        facilement et rapidement. Nous offrons un large choix de trajets, des tarifs compétitifs
        et un service client à votre écoute.
    </p>
    <a href="#" class="cta-btn" role="button" aria-label="En savoir plus sur TrainTicket">
        En savoir plus
    </a>
</section>

<!-- Footer -->
<footer class="footer" role="contentinfo">
    <p>&copy; 2025 TrainTicket. Tous droits réservés.</p>
    <div class="footer-links" role="navigation" aria-label="Liens de pied de page">
        <a href="#">Politique de confidentialité</a>
        <a href="#">Conditions d'utilisation</a>
        <a href="#">Contact</a>
    </div>
    <div class="social-icons" aria-label="Réseaux sociaux">
        <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
    </div>
</footer>
</body>
</html>