<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recherche de Trains - TrainApp</title>
    
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .search-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .search-form {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        }
        
        .form-input {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f9fafb;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            background: white;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .btn-search {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            padding: 1rem 2rem;
            border-radius: 12px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }
        
        .btn-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
        }
        
        .train-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #f1f5f9;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .train-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .status-available {
            background: #dcfce7;
            color: #166534;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-limited {
            background: #fef3c7;
            color: #92400e;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-full {
            background: #fee2e2;
            color: #991b1b;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .route-line {
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            position: relative;
            margin: 0 1rem;
        }
        
        .route-dot {
            width: 12px;
            height: 12px;
            background: #3b82f6;
            border-radius: 50%;
            position: absolute;
            top: -5px;
        }
        
        .route-dot.start {
            left: -6px;
        }
        
        .route-dot.end {
            right: -6px;
        }
        
        .loading-spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hero-section {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(139, 92, 246, 0.9) 100%);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }
        
        .floating-icon {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-train text-3xl text-blue-600 mr-3"></i>
                    <h1 class="text-2xl font-bold text-gray-900">TrainApp</h1>
                </div>
                
                <nav class="flex space-x-6">
                    <a href="<%= request.getContextPath() %>/views/index.jsp" class="text-gray-600 hover:text-blue-600 font-medium">
                        <i class="fas fa-home mr-2"></i>Accueil
                    </a>
                    <a href="<%= request.getContextPath() %>/views/login.jsp" class="text-gray-600 hover:text-blue-600 font-medium">
                        <i class="fas fa-sign-in-alt mr-2"></i>Connexion
                    </a>
                    <a href="<%= request.getContextPath() %>/views/register.jsp" class="text-gray-600 hover:text-blue-600 font-medium">
                        <i class="fas fa-user-plus mr-2"></i>Inscription
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="max-w-4xl mx-auto px-4">
            <div class="floating-icon mb-6">
                <i class="fas fa-search text-6xl opacity-80"></i>
            </div>
            <h1 class="text-5xl font-bold mb-4">Trouvez votre train idéal</h1>
            <p class="text-xl opacity-90 mb-8">Recherchez et réservez vos billets de train en quelques clics</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Search Form -->
        <div class="search-container p-8 mb-12">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Rechercher un train</h2>
                <p class="text-gray-600">Renseignez vos critères de voyage pour trouver les meilleurs trajets</p>
            </div>
            
            <form id="searchForm" class="search-form p-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div>
                        <label for="departureCity" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-map-marker-alt text-blue-600 mr-2"></i>Ville de départ
                        </label>
                        <input type="text" id="departureCity" name="departureCity" 
                               class="form-input w-full" placeholder="Ex: Paris" required>
                    </div>
                    
                    <div>
                        <label for="destinationCity" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-flag-checkered text-green-600 mr-2"></i>Ville de destination
                        </label>
                        <input type="text" id="destinationCity" name="destinationCity" 
                               class="form-input w-full" placeholder="Ex: Lyon" required>
                    </div>
                    
                    <div>
                        <label for="travelDate" class="block text-sm font-semibold text-gray-700 mb-2">
                            <i class="fas fa-calendar-alt text-purple-600 mr-2"></i>Date de voyage
                        </label>
                        <input type="date" id="travelDate" name="travelDate" 
                               class="form-input w-full" required>
                    </div>
                    
                    <div class="flex items-end">
                        <button type="submit" class="btn-search w-full">
                            <i class="fas fa-search mr-2"></i>Rechercher
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Search Results -->
        <div id="searchResults" class="hidden">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-gray-900">Résultats de recherche</h3>
                <div class="text-sm text-gray-600">
                    <span id="resultsCount">0</span> trajet(s) trouvé(s)
                </div>
            </div>
            
            <div id="resultsContainer" class="space-y-6">
                <!-- Les résultats seront affichés ici -->
            </div>
        </div>

        <!-- Loading State -->
        <div id="loadingState" class="hidden text-center py-12">
            <div class="loading-spinner mx-auto mb-4"></div>
            <p class="text-gray-600 text-lg">Recherche en cours...</p>
        </div>

        <!-- No Results State -->
        <div id="noResultsState" class="hidden text-center py-12">
            <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-700 mb-2">Aucun trajet trouvé</h3>
            <p class="text-gray-600">Essayez de modifier vos critères de recherche</p>
        </div>

        <!-- Error State -->
        <div id="errorState" class="hidden text-center py-12">
            <i class="fas fa-exclamation-triangle text-6xl text-red-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-700 mb-2">Erreur de recherche</h3>
            <p class="text-gray-600" id="errorMessage">Une erreur est survenue lors de la recherche</p>
            <button onclick="retrySearch()" class="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                <i class="fas fa-redo mr-2"></i>Réessayer
            </button>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="flex justify-center items-center mb-4">
                <i class="fas fa-train text-2xl mr-3"></i>
                <span class="text-xl font-bold">TrainApp</span>
            </div>
            <p class="text-gray-400">Votre compagnon de voyage pour tous vos déplacements en train</p>
        </div>
    </footer>

    <script>
        // Variables globales
        let lastSearchParams = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Définir la date minimale à aujourd'hui
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('travelDate').min = today;
            document.getElementById('travelDate').value = today;

            // Récupérer les paramètres URL pour la recherche rapide
            const urlParams = new URLSearchParams(window.location.search);
            const departureCity = urlParams.get('departureCity');
            const destinationCity = urlParams.get('destinationCity');
            const travelDate = urlParams.get('travelDate');

            // Pré-remplir les champs si des paramètres sont présents
            if (departureCity) {
                document.getElementById('departureCity').value = departureCity;
            }
            if (destinationCity) {
                document.getElementById('destinationCity').value = destinationCity;
            }
            if (travelDate) {
                document.getElementById('travelDate').value = travelDate;
            }

            // Effectuer automatiquement la recherche si tous les paramètres sont présents
            if (departureCity && destinationCity && travelDate) {
                setTimeout(() => {
                    performSearch();
                }, 500); // Petit délai pour permettre à la page de se charger complètement
            }

            // Gestionnaire de soumission du formulaire
            document.getElementById('searchForm').addEventListener('submit', function(e) {
                e.preventDefault();
                performSearch();
            });
        });

        // Fonction principale de recherche
        function performSearch() {
            const departureCity = document.getElementById('departureCity').value.trim();
            const destinationCity = document.getElementById('destinationCity').value.trim();
            const travelDate = document.getElementById('travelDate').value;

            // Validation
            if (!departureCity || !destinationCity || !travelDate) {
                showError('Veuillez remplir tous les champs de recherche');
                return;
            }

            if (departureCity.toLowerCase() === destinationCity.toLowerCase()) {
                showError('La ville de départ et la destination doivent être différentes');
                return;
            }

            // Sauvegarder les paramètres pour retry
            lastSearchParams = { departureCity, destinationCity, travelDate };

            // Afficher l'état de chargement
            showLoadingState();

            // Construire l'URL de recherche
            const searchParams = new URLSearchParams({
                action: 'search',
                departureCity: departureCity,
                destinationCity: destinationCity,
                travelDate: travelDate
            });

            // Effectuer la recherche
            fetch('<%= request.getContextPath() %>/api/trips-search?' + searchParams.toString())
                .then(response => response.json())
                .then(data => {
                    hideAllStates();
                    if (data.success) {
                        displayResults(data.trips || []);
                    } else {
                        showError(data.message || 'Erreur lors de la recherche');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    hideAllStates();
                    showError('Erreur de connexion. Veuillez réessayer.');
                });
        }

        // Afficher les résultats
        function displayResults(trips) {
            const resultsContainer = document.getElementById('resultsContainer');
            const resultsCount = document.getElementById('resultsCount');
            const searchResults = document.getElementById('searchResults');

            resultsCount.textContent = trips.length;

            if (trips.length === 0) {
                showNoResultsState();
                return;
            }

            resultsContainer.innerHTML = trips.map(trip => createTripCard(trip)).join('');
            searchResults.classList.remove('hidden');
        }

        // Créer une carte de train
        function createTripCard(trip) {
            const departureTime = formatTime(trip.departureTime);
            const arrivalTime = formatTime(trip.arrivalTime);
            const duration = calculateDuration(trip.departureTime, trip.arrivalTime);
            const availability = getAvailabilityInfo(trip.availableSeats);

            return `
                <div class="train-card p-6">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <!-- Route et horaires -->
                        <div class="flex-1 mb-4 lg:mb-0">
                            <div class="flex items-center justify-between mb-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gray-900">\${departureTime}</div>
                                    <div class="text-sm text-gray-600">\${trip.departureCity}</div>
                                </div>

                                <div class="flex-1 mx-8">
                                    <div class="route-line">
                                        <div class="route-dot start"></div>
                                        <div class="route-dot end"></div>
                                    </div>
                                    <div class="text-center mt-2">
                                        <span class="text-sm text-gray-500">
                                            <i class="fas fa-clock mr-1"></i>\${duration}
                                        </span>
                                    </div>
                                </div>

                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gray-900">\${arrivalTime}</div>
                                    <div class="text-sm text-gray-600">\${trip.destinationCity}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Prix et disponibilité -->
                        <div class="flex flex-col lg:flex-row lg:items-center lg:space-x-6">
                            <div class="text-center lg:text-right mb-4 lg:mb-0">
                                <div class="text-3xl font-bold text-blue-600 mb-1">
                                    \${trip.price.toFixed(2)} €
                                </div>
                                <div class="text-sm text-gray-600">par personne</div>
                            </div>

                            <div class="text-center">
                                <div class="mb-3">
                                    <span class="\${availability.cssClass}">
                                        \${availability.text}
                                    </span>
                                </div>
                                <div class="text-sm text-gray-600 mb-3">
                                    <i class="fas fa-chair mr-1"></i>
                                    \${trip.availableSeats} place(s) disponible(s)
                                </div>
                                <button onclick="selectTrip(\${trip.id})"
                                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors duration-200 \${trip.availableSeats === 0 ? 'opacity-50 cursor-not-allowed' : ''}"
                                        \${trip.availableSeats === 0 ? 'disabled' : ''}>
                                    <i class="fas fa-ticket-alt mr-2"></i>
                                    \${trip.availableSeats === 0 ? 'Complet' : 'Réserver'}
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Informations supplémentaires -->
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <div class="flex flex-wrap items-center text-sm text-gray-600 space-x-6">
                            <span>
                                <i class="fas fa-train mr-1 text-blue-500"></i>
                                Train #\${trip.id}
                            </span>
                            <span>
                                <i class="fas fa-calendar mr-1 text-green-500"></i>
                                \${formatDate(trip.departureTime)}
                            </span>
                            <span>
                                <i class="fas fa-info-circle mr-1 text-purple-500"></i>
                                Trajet direct
                            </span>
                        </div>
                    </div>
                </div>
            `;
        }

        // Obtenir les informations de disponibilité
        function getAvailabilityInfo(availableSeats) {
            if (availableSeats === 0) {
                return {
                    cssClass: 'status-full',
                    text: 'Complet'
                };
            } else if (availableSeats <= 10) {
                return {
                    cssClass: 'status-limited',
                    text: 'Places limitées'
                };
            } else {
                return {
                    cssClass: 'status-available',
                    text: 'Disponible'
                };
            }
        }

        // Calculer la durée du voyage
        function calculateDuration(departureTime, arrivalTime) {
            const departure = new Date(departureTime);
            const arrival = new Date(arrivalTime);
            const diffMs = arrival - departure;
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

            if (diffHours === 0) {
                return `${diffMinutes}min`;
            } else if (diffMinutes === 0) {
                return `${diffHours}h`;
            } else {
                return `${diffHours}h ${diffMinutes}min`;
            }
        }

        // Formater l'heure
        function formatTime(dateTimeString) {
            const date = new Date(dateTimeString);
            return date.toLocaleTimeString('fr-FR', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Formater la date
        function formatDate(dateTimeString) {
            const date = new Date(dateTimeString);
            return date.toLocaleDateString('fr-FR', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        // Sélectionner un train pour réservation
        function selectTrip(tripId) {
            // Rediriger vers la page de connexion avec le trip sélectionné
            const loginUrl = '<%= request.getContextPath() %>/views/login.jsp?redirect=reservation&tripId=' + tripId;
            window.location.href = loginUrl;
        }

        // Gestion des états d'affichage
        function showLoadingState() {
            hideAllStates();
            document.getElementById('loadingState').classList.remove('hidden');
        }

        function showNoResultsState() {
            hideAllStates();
            document.getElementById('noResultsState').classList.remove('hidden');
        }

        function showError(message) {
            hideAllStates();
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorState').classList.remove('hidden');
        }

        function hideAllStates() {
            document.getElementById('searchResults').classList.add('hidden');
            document.getElementById('loadingState').classList.add('hidden');
            document.getElementById('noResultsState').classList.add('hidden');
            document.getElementById('errorState').classList.add('hidden');
        }

        // Réessayer la recherche
        function retrySearch() {
            if (lastSearchParams) {
                document.getElementById('departureCity').value = lastSearchParams.departureCity;
                document.getElementById('destinationCity').value = lastSearchParams.destinationCity;
                document.getElementById('travelDate').value = lastSearchParams.travelDate;
                performSearch();
            }
        }
    </script>
</body>
</html>
