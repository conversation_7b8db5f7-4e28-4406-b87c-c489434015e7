<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="Modal.User" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null) {
        response.sendRedirect(request.getContextPath() + "/views/login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes Réservations - TrainApp</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .reservation-card {
            transition: all 0.3s ease;
        }
        .reservation-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 16px 24px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }
        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }
        .notification.success {
            background-color: #10b981;
        }
        .notification.error {
            background-color: #ef4444;
        }
        .status-confirmed {
            background-color: #10b981;
            color: white;
        }
        .status-cancelled {
            background-color: #ef4444;
            color: white;
        }
        .status-pending {
            background-color: #f59e0b;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-blue-600 text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <i class="fas fa-train text-2xl mr-3"></i>
                    <span class="text-xl font-bold">TrainApp</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="<%= request.getContextPath() %>/views/trips.jsp" class="hover:bg-blue-700 px-3 py-2 rounded-md">
                        <i class="fas fa-route mr-2"></i>Trajets
                    </a>
                    <a href="<%= request.getContextPath() %>/views/reservations.jsp" class="bg-blue-700 px-3 py-2 rounded-md">
                        <i class="fas fa-ticket-alt mr-2"></i>Mes Réservations
                    </a>
                    <div class="flex items-center">
                        <i class="fas fa-user-circle text-xl mr-2"></i>
                        <span><%= user.getUsername() %></span>
                    </div>
                    <a href="<%= request.getContextPath() %>/logout" class="bg-red-500 hover:bg-red-600 px-3 py-2 rounded-md">
                        <i class="fas fa-sign-out-alt mr-2"></i>Déconnexion
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4">
                <h1 class="text-2xl font-bold text-gray-900">
                    <i class="fas fa-ticket-alt text-blue-600 mr-3"></i>
                    Mes Réservations
                </h1>
                <p class="text-gray-600 mt-2">Gérez toutes vos réservations de trajets</p>
            </div>
        </div>

        <!-- Statistiques des réservations -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-ticket-alt text-2xl text-blue-600"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total réservations</dt>
                                <dd class="text-lg font-medium text-gray-900" id="totalReservations">-</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-2xl text-green-600"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Confirmées</dt>
                                <dd class="text-lg font-medium text-gray-900" id="confirmedReservations">-</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-times-circle text-2xl text-red-600"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Annulées</dt>
                                <dd class="text-lg font-medium text-gray-900" id="cancelledReservations">-</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-euro-sign text-2xl text-yellow-600"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total dépensé</dt>
                                <dd class="text-lg font-medium text-gray-900" id="totalSpent">-</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-bolt text-yellow-600 mr-2"></i>
                        Actions rapides
                    </h2>
                    <div class="flex space-x-3">
                        <button onclick="loadReservations()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-200">
                            <i class="fas fa-sync-alt mr-2"></i>Actualiser
                        </button>
                        <a href="<%= request.getContextPath() %>/views/trips.jsp" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition duration-200">
                            <i class="fas fa-plus mr-2"></i>Nouvelle réservation
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Liste des réservations -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-list text-blue-600 mr-2"></i>
                    Toutes mes réservations
                </h2>
            </div>
            <div class="p-6">
                <div id="reservationsContainer">
                    <!-- Les réservations seront chargées ici -->
                    <div class="text-center py-8">
                        <i class="fas fa-spinner fa-spin text-3xl text-blue-600 mb-4"></i>
                        <p class="text-gray-600">Chargement des réservations...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de confirmation d'annulation -->
    <div id="cancelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                Annuler la réservation
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500">
                                    Êtes-vous sûr de vouloir annuler cette réservation ? Cette action ne peut pas être annulée.
                                </p>
                                <div id="cancelReservationDetails" class="mt-3 p-3 bg-gray-50 rounded-md text-sm">
                                    <!-- Détails de la réservation à annuler -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" onclick="confirmCancellation()" 
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        <i class="fas fa-times mr-2"></i>Annuler la réservation
                    </button>
                    <button type="button" onclick="closeCancelModal()" 
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Garder la réservation
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification">
        <span id="notificationMessage"></span>
    </div>

    <script>
        let reservations = [];
        let reservationToCancel = null;

        // Charger les réservations au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            loadReservations();
            loadStats();
        });

        // Fonction pour charger les réservations
        async function loadReservations() {
            try {
                const response = await fetch('<%= request.getContextPath() %>/api/reservations?action=list');
                const result = await response.json();

                if (result.success) {
                    reservations = result.reservations;
                    displayReservations(reservations);
                } else {
                    showNotification('Erreur lors du chargement des réservations', 'error');
                }
            } catch (error) {
                console.error('Erreur:', error);
                showNotification('Erreur de connexion', 'error');
            }
        }

        // Fonction pour charger les statistiques
        async function loadStats() {
            try {
                const response = await fetch('<%= request.getContextPath() %>/api/reservations?action=stats');
                const result = await response.json();

                if (result.success) {
                    document.getElementById('totalReservations').textContent = result.totalReservations;
                    document.getElementById('confirmedReservations').textContent = result.confirmedReservations;
                    document.getElementById('cancelledReservations').textContent = result.cancelledReservations;
                    document.getElementById('totalSpent').textContent = result.totalSpent.toFixed(2) + ' €';
                }
            } catch (error) {
                console.error('Erreur:', error);
            }
        }

        // Fonction pour afficher les réservations
        function displayReservations(reservations) {
            const container = document.getElementById('reservationsContainer');
            
            if (reservations.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-12">
                        <i class="fas fa-ticket-alt text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune réservation</h3>
                        <p class="text-gray-600 mb-6">Vous n'avez pas encore effectué de réservation.</p>
                        <a href="<%= request.getContextPath() %>/views/trips.jsp" 
                           class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition duration-200">
                            <i class="fas fa-plus mr-2"></i>Faire ma première réservation
                        </a>
                    </div>
                `;
                return;
            }

            container.innerHTML = `
                <div class="space-y-4">
                    ${reservations.map(reservation => `
                        <div class="reservation-card border border-gray-200 rounded-lg p-6 ${reservation.status === 'CANCELLED' ? 'opacity-60' : ''}">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-map-marker-alt text-blue-600 mr-2"></i>
                                        <span class="font-semibold text-gray-900">${reservation.departureCity}</span>
                                    </div>
                                    <i class="fas fa-arrow-right text-gray-400"></i>
                                    <div class="flex items-center">
                                        <i class="fas fa-map-marker-alt text-red-600 mr-2"></i>
                                        <span class="font-semibold text-gray-900">${reservation.destinationCity}</span>
                                    </div>
                                </div>
                                <span class="px-3 py-1 rounded-full text-xs font-semibold status-${reservation.status.toLowerCase()}">
                                    ${getStatusText(reservation.status)}
                                </span>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-calendar mr-2"></i>
                                    <span>Réservé le ${formatDate(reservation.reservationDate)}</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-clock mr-2"></i>
                                    <span>Départ: ${formatDateTime(reservation.departureTime)}</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-chair mr-2"></i>
                                    <span>${reservation.numberOfSeats} place(s)</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-euro-sign mr-2"></i>
                                    <span class="font-semibold">${reservation.totalPrice.toFixed(2)} €</span>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">
                                    Réservation #${reservation.id}
                                </div>
                                <div class="flex space-x-2">
                                    ${reservation.status === 'CONFIRMED' ? `
                                        <button onclick="openCancelModal(${reservation.id})" 
                                                class="px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition duration-200 text-sm">
                                            <i class="fas fa-times mr-1"></i>Annuler
                                        </button>
                                    ` : ''}
                                    <button onclick="showReservationDetails(${reservation.id})" 
                                            class="px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition duration-200 text-sm">
                                        <i class="fas fa-eye mr-1"></i>Détails
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // Fonction pour ouvrir le modal d'annulation
        function openCancelModal(reservationId) {
            const reservation = reservations.find(r => r.id === reservationId);
            if (!reservation) return;

            reservationToCancel = reservation;
            
            document.getElementById('cancelReservationDetails').innerHTML = `
                <div class="font-semibold mb-2">${reservation.departureCity} → ${reservation.destinationCity}</div>
                <div class="text-gray-600">
                    <div>Départ: ${formatDateTime(reservation.departureTime)}</div>
                    <div>Places: ${reservation.numberOfSeats}</div>
                    <div>Prix: ${reservation.totalPrice.toFixed(2)} €</div>
                </div>
            `;
            
            document.getElementById('cancelModal').classList.remove('hidden');
        }

        // Fonction pour fermer le modal d'annulation
        function closeCancelModal() {
            document.getElementById('cancelModal').classList.add('hidden');
            reservationToCancel = null;
        }

        // Fonction pour confirmer l'annulation
        async function confirmCancellation() {
            if (!reservationToCancel) return;

            try {
                const response = await fetch('<%= request.getContextPath() %>/api/reservations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=cancel&reservationId=${reservationToCancel.id}`
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('Réservation annulée avec succès', 'success');
                    closeCancelModal();
                    loadReservations();
                    loadStats();
                } else {
                    showNotification('Erreur: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('Erreur:', error);
                showNotification('Erreur de connexion', 'error');
            }
        }

        // Fonction pour afficher les détails d'une réservation
        function showReservationDetails(reservationId) {
            const reservation = reservations.find(r => r.id === reservationId);
            if (!reservation) return;

            alert(`Détails de la réservation #${reservation.id}:\n\n` +
                  `Trajet: ${reservation.departureCity} → ${reservation.destinationCity}\n` +
                  `Départ: ${formatDateTime(reservation.departureTime)}\n` +
                  `Arrivée: ${formatDateTime(reservation.arrivalTime)}\n` +
                  `Places: ${reservation.numberOfSeats}\n` +
                  `Prix total: ${reservation.totalPrice.toFixed(2)} €\n` +
                  `Statut: ${getStatusText(reservation.status)}\n` +
                  `Réservé le: ${formatDateTime(reservation.reservationDate)}`);
        }

        // Fonction pour obtenir le texte du statut
        function getStatusText(status) {
            switch(status) {
                case 'CONFIRMED': return 'Confirmée';
                case 'CANCELLED': return 'Annulée';
                case 'PENDING': return 'En attente';
                default: return status;
            }
        }

        // Fonction pour formater la date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        }

        // Fonction pour formater la date et l'heure
        function formatDateTime(dateTimeString) {
            const date = new Date(dateTimeString);
            return date.toLocaleDateString('fr-FR') + ' à ' + date.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
        }

        // Fonction pour afficher les notifications
        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            const messageElement = document.getElementById('notificationMessage');
            
            messageElement.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 5000);
        }
    </script>
</body>
</html>
