<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="Modal.User" %>
<%
    User user = (User) session.getAttribute("user");
%>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tous les Voyages - TrainApp</title>
    
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .nav-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .nav-link {
            color: #374151;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
        }
        
        .nav-link:hover {
            color: #10b981;
            background: rgba(16, 185, 129, 0.1);
            text-decoration: none;
        }
        
        .logo-text {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .trips-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .filter-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
            margin: 0.25rem;
        }
        
        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
        
        .filter-btn.active {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }
        
        .filter-btn.active:hover {
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }
        
        .trip-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid #f1f5f9;
            margin-bottom: 1.5rem;
        }
        
        .trip-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .route-line {
            position: relative;
            height: 3px;
            background: linear-gradient(90deg, #10b981 0%, #3b82f6 100%);
            margin: 1rem 0;
            border-radius: 2px;
        }
        
        .route-dot {
            position: absolute;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background: white;
            border: 3px solid #10b981;
            top: -5.5px;
        }
        
        .route-dot.start {
            left: 0;
        }
        
        .route-dot.end {
            right: 0;
            border-color: #3b82f6;
        }
        
        .status-available {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-limited {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-full {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .loading-spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #10b981;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stats-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #10b981;
            margin-bottom: 0.5rem;
            line-height: 1;
        }
        
        .stats-label {
            color: #6b7280;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .sort-select {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            background: white;
        }
        
        .sort-select:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="nav-container sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="index.jsp" class="logo-text">
                        <i class="fas fa-train mr-2"></i>TrainApp
                    </a>
                </div>
                
                <div class="hidden md:flex items-center space-x-6">
                    <a href="index.jsp" class="nav-link">
                        <i class="fas fa-home mr-1"></i>Accueil
                    </a>
                    <a href="search-trains.jsp" class="nav-link">
                        <i class="fas fa-search mr-1"></i>Rechercher
                    </a>
                    <a href="all-trips.jsp" class="nav-link" style="color: #10b981; background: rgba(16, 185, 129, 0.1);">
                        <i class="fas fa-list mr-1"></i>Tous les voyages
                    </a>
                    <% if (user != null) { %>
                        <a href="dashboard.jsp" class="nav-link">
                            <i class="fas fa-tachometer-alt mr-1"></i>Dashboard
                        </a>
                        <a href="../logout" class="nav-link">
                            <i class="fas fa-sign-out-alt mr-1"></i>Déconnexion
                        </a>
                    <% } else { %>
                        <a href="login.jsp" class="nav-link">
                            <i class="fas fa-sign-in-alt mr-1"></i>Connexion
                        </a>
                        <a href="register.jsp" class="nav-link">
                            <i class="fas fa-user-plus mr-1"></i>Inscription
                        </a>
                    <% } %>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">
                    <i class="fas fa-list text-green-600 mr-3 floating-animation"></i>
                    Tous les Voyages Disponibles
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Découvrez tous les voyages en train disponibles. Filtrez par date et triez selon vos préférences.
                </p>
            </div>

            <!-- Statistiques -->
            <div id="statsSection" class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
                <div class="stats-card">
                    <div class="stats-number" id="totalTrips">-</div>
                    <div class="stats-label">Total voyages</div>
                </div>
                <div class="stats-card">
                    <div class="stats-number" id="todayTrips">-</div>
                    <div class="stats-label">Aujourd'hui</div>
                </div>
                <div class="stats-card">
                    <div class="stats-number" id="tomorrowTrips">-</div>
                    <div class="stats-label">Demain</div>
                </div>
                <div class="stats-card">
                    <div class="stats-number" id="weekTrips">-</div>
                    <div class="stats-label">Cette semaine</div>
                </div>
            </div>

            <!-- Filtres et tri -->
            <div class="max-w-6xl mx-auto mb-8">
                <div class="trips-container p-6">
                    <div class="flex flex-wrap items-center justify-between gap-4">
                        <!-- Filtres par date -->
                        <div class="flex flex-wrap gap-2">
                            <button class="filter-btn active" data-filter="all" onclick="filterTrips('all')">
                                <i class="fas fa-globe mr-2"></i>Tous
                            </button>
                            <button class="filter-btn" data-filter="today" onclick="filterTrips('today')">
                                <i class="fas fa-calendar-day mr-2"></i>Aujourd'hui
                            </button>
                            <button class="filter-btn" data-filter="tomorrow" onclick="filterTrips('tomorrow')">
                                <i class="fas fa-calendar-plus mr-2"></i>Demain
                            </button>
                            <button class="filter-btn" data-filter="week" onclick="filterTrips('week')">
                                <i class="fas fa-calendar-week mr-2"></i>Cette semaine
                            </button>
                            <button class="filter-btn" data-filter="future" onclick="filterTrips('future')">
                                <i class="fas fa-calendar-alt mr-2"></i>À venir
                            </button>
                        </div>
                        
                        <!-- Tri -->
                        <div class="flex items-center gap-4">
                            <label for="sortSelect" class="text-sm font-semibold text-gray-700">
                                <i class="fas fa-sort mr-1"></i>Trier par:
                            </label>
                            <select id="sortSelect" class="sort-select" onchange="sortTrips()">
                                <option value="departure_time-asc">Date de départ ↑</option>
                                <option value="departure_time-desc">Date de départ ↓</option>
                                <option value="price-asc">Prix ↑</option>
                                <option value="price-desc">Prix ↓</option>
                                <option value="destination_city-asc">Destination A-Z</option>
                                <option value="destination_city-desc">Destination Z-A</option>
                                <option value="available_seats-desc">Places disponibles ↓</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Résultats -->
            <div id="tripsResults" class="max-w-6xl mx-auto">
                <!-- Loading initial -->
                <div id="loadingState" class="text-center py-12">
                    <div class="loading-spinner mx-auto mb-4"></div>
                    <p class="text-gray-600">Chargement des voyages...</p>
                </div>
            </div>

            <!-- Template pour les cartes de voyage (caché) -->
            <div id="tripCardTemplate" style="display: none;">
                <div class="trip-card">
                    <div class="flex flex-wrap items-center justify-between gap-4">
                        <!-- Route info -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-xl font-bold text-gray-900">
                                    <span class="departure-city"></span> → <span class="destination-city"></span>
                                </h3>
                                <span class="status-badge"></span>
                            </div>

                            <div class="route-line">
                                <div class="route-dot start"></div>
                                <div class="route-dot end"></div>
                            </div>

                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                    <i class="fas fa-clock text-green-600 mr-1"></i>
                                    <span class="font-semibold">Départ:</span><br>
                                    <span class="departure-time"></span>
                                </div>
                                <div>
                                    <i class="fas fa-flag-checkered text-blue-600 mr-1"></i>
                                    <span class="font-semibold">Arrivée:</span><br>
                                    <span class="arrival-time"></span>
                                </div>
                                <div>
                                    <i class="fas fa-hourglass-half text-purple-600 mr-1"></i>
                                    <span class="font-semibold">Durée:</span><br>
                                    <span class="duration"></span>
                                </div>
                                <div>
                                    <i class="fas fa-users text-orange-600 mr-1"></i>
                                    <span class="font-semibold">Places:</span><br>
                                    <span class="available-seats"></span> disponibles
                                </div>
                            </div>
                        </div>

                        <!-- Prix et action -->
                        <div class="text-center">
                            <div class="text-3xl font-bold text-green-600 mb-2">
                                <span class="price"></span>€
                            </div>
                            <button class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-lg transition-colors duration-200 reserve-btn">
                                <i class="fas fa-ticket-alt mr-2"></i>
                                Réserver
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p>&copy; 2025 TrainApp. Tous droits réservés.</p>
        </div>
    </footer>

    <script>
        // Variables globales
        let allTrips = [];
        let currentFilter = 'all';
        let isLoading = false;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadAllTrips();
            loadStats();
        });

        // Charger tous les voyages
        function loadAllTrips() {
            if (isLoading) return;
            
            isLoading = true;
            showLoading();
            
            const sortSelect = document.getElementById('sortSelect');
            const [sortBy, order] = sortSelect.value.split('-');
            
            const apiUrl = '../api/all-trips?action=getAll&sortBy=' + encodeURIComponent(sortBy) + 
                          '&order=' + encodeURIComponent(order);
            
            fetch(apiUrl)
                .then(response => response.json())
                .then(data => {
                    isLoading = false;
                    hideLoading();
                    
                    if (data.success) {
                        allTrips = data.data;
                        displayTrips(allTrips);
                        updateStats();
                    } else {
                        showError(data.message || 'Erreur lors du chargement des voyages');
                    }
                })
                .catch(error => {
                    isLoading = false;
                    hideLoading();
                    console.error('Erreur:', error);
                    showError('Erreur de connexion. Veuillez réessayer.');
                });
        }

        // Charger les statistiques
        function loadStats() {
            // Charger les stats pour différentes périodes
            const periods = ['today', 'tomorrow', 'week'];
            
            periods.forEach(period => {
                fetch('../api/all-trips?action=getByDate&date=' + period)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            document.getElementById(period + 'Trips').textContent = data.total;
                        }
                    })
                    .catch(error => console.error('Erreur stats:', error));
            });
        }

        // Filtrer les voyages
        function filterTrips(filter) {
            currentFilter = filter;
            
            // Mettre à jour les boutons actifs
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector('[data-filter="' + filter + '"]').classList.add('active');
            
            if (filter === 'all') {
                displayTrips(allTrips);
            } else {
                showLoading();
                
                fetch('../api/all-trips?action=getByDate&date=' + filter)
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();
                        if (data.success) {
                            displayTrips(data.data);
                        } else {
                            showError(data.message);
                        }
                    })
                    .catch(error => {
                        hideLoading();
                        console.error('Erreur:', error);
                        showError('Erreur lors du filtrage');
                    });
            }
        }

        // Trier les voyages
        function sortTrips() {
            loadAllTrips(); // Recharger avec le nouveau tri
        }

        // Afficher les voyages
        function displayTrips(trips) {
            const resultsContainer = document.getElementById('tripsResults');

            if (trips.length === 0) {
                resultsContainer.innerHTML =
                    '<div class="text-center py-12">' +
                        '<i class="fas fa-train text-6xl text-gray-300 mb-4"></i>' +
                        '<h3 class="text-xl font-semibold text-gray-600 mb-2">Aucun voyage trouvé</h3>' +
                        '<p class="text-gray-500">Essayez de modifier vos filtres ou revenez plus tard.</p>' +
                    '</div>';
                return;
            }

            // Créer le conteneur principal
            const container = document.createElement('div');
            container.className = 'grid grid-cols-1 gap-6';

            // Template de base
            const template = document.getElementById('tripCardTemplate');

            trips.forEach(trip => {
                // Cloner le template
                const tripCard = template.cloneNode(true);
                tripCard.style.display = 'block';
                tripCard.id = '';

                // Calculer la durée
                const departureDate = new Date(trip.departureTime);
                const arrivalDate = new Date(trip.arrivalTime);
                const duration = Math.round((arrivalDate - departureDate) / (1000 * 60 * 60 * 100)) / 10;

                // Remplir les données
                tripCard.querySelector('.departure-city').textContent = trip.departureCity;
                tripCard.querySelector('.destination-city').textContent = trip.destinationCity;
                tripCard.querySelector('.departure-time').textContent = formatDateTime(trip.departureTime);
                tripCard.querySelector('.arrival-time').textContent = formatDateTime(trip.arrivalTime);
                tripCard.querySelector('.duration').textContent = duration + 'h';
                tripCard.querySelector('.available-seats').textContent = trip.availableSeats;
                tripCard.querySelector('.price').textContent = trip.price.toFixed(2);

                // Statut
                const statusBadge = tripCard.querySelector('.status-badge');
                statusBadge.className = 'status-badge ' + getStatusClass(trip.availableSeats);
                statusBadge.textContent = getStatusText(trip.availableSeats);

                // Bouton de réservation
                const reserveBtn = tripCard.querySelector('.reserve-btn');
                reserveBtn.onclick = function() { selectTrip(trip.id); };

                // Ajouter au conteneur
                container.appendChild(tripCard.firstElementChild);
            });

            // Remplacer le contenu
            resultsContainer.innerHTML = '';
            resultsContainer.appendChild(container);

            // Mettre à jour le total affiché
            document.getElementById('totalTrips').textContent = allTrips.length;
        }

        // Fonctions utilitaires
        function getStatusClass(availableSeats) {
            if (availableSeats > 50) return 'status-available';
            if (availableSeats > 10) return 'status-limited';
            return 'status-full';
        }

        function getStatusText(availableSeats) {
            if (availableSeats > 50) return 'Disponible';
            if (availableSeats > 10) return 'Places limitées';
            if (availableSeats > 0) return 'Dernières places';
            return 'Complet';
        }

        function formatDateTime(dateTimeString) {
            const date = new Date(dateTimeString);
            const options = {
                weekday: 'short',
                day: '2-digit',
                month: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            };
            return date.toLocaleDateString('fr-FR', options);
        }

        function updateStats() {
            if (allTrips.length > 0) {
                document.getElementById('totalTrips').textContent = allTrips.length;
            }
        }

        function showLoading() {
            document.getElementById('tripsResults').innerHTML =
                '<div id="loadingState" class="text-center py-12">' +
                    '<div class="loading-spinner mx-auto mb-4"></div>' +
                    '<p class="text-gray-600">Chargement des voyages...</p>' +
                '</div>';
        }

        function hideLoading() {
            const loadingState = document.getElementById('loadingState');
            if (loadingState) {
                loadingState.remove();
            }
        }

        function showError(message) {
            document.getElementById('tripsResults').innerHTML =
                '<div class="text-center py-12">' +
                    '<i class="fas fa-exclamation-triangle text-6xl text-red-400 mb-4"></i>' +
                    '<h3 class="text-xl font-semibold text-gray-600 mb-2">Erreur</h3>' +
                    '<p class="text-gray-500">' + message + '</p>' +
                    '<button onclick="loadAllTrips()" class="mt-4 bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">' +
                        '<i class="fas fa-redo mr-2"></i>Réessayer' +
                    '</button>' +
                '</div>';
        }

        // Sélectionner un voyage pour réservation
        function selectTrip(tripId) {
            <% if (user != null) { %>
                // Utilisateur connecté - rediriger vers la page de réservation
                window.location.href = 'reservation.jsp?tripId=' + tripId;
            <% } else { %>
                // Utilisateur non connecté - rediriger vers la connexion
                window.location.href = 'login.jsp?redirect=reservation&tripId=' + tripId;
            <% } %>
        }
    </script>
</body>
</html>
